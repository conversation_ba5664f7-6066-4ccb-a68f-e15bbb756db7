{"__meta": {"id": "X9a0d614120387fb3a190824ad69c24a6", "datetime": "2025-06-17 15:16:04", "utime": **********.983911, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173363.374924, "end": **********.983955, "duration": 1.6090309619903564, "duration_str": "1.61s", "measures": [{"label": "Booting", "start": 1750173363.374924, "relative_start": 0, "end": **********.753093, "relative_end": **********.753093, "duration": 1.378169059753418, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.753122, "relative_start": 1.3781981468200684, "end": **********.98396, "relative_end": 5.0067901611328125e-06, "duration": 0.23083782196044922, "duration_str": "231ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46093176, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.009800000000000001, "accumulated_duration_str": "9.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8817, "duration": 0.00687, "duration_str": "6.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.102}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.928616, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.102, "width_percent": 14.796}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.95436, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.898, "width_percent": 15.102}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1613361381 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxkVURUY3JRUmhYVGZscDNxWVBaZEE9PSIsInZhbHVlIjoiQVhBQ1pYWFp2SXhOei9sVXllUmgvWGwwbVlLUnNhWi83d1FJM2E1QzJlWFhYajh6S1puTGpxQUo3cHBPWE5UVFhZdXVrVjlkRlFyVHJicnhtZmE0NmljaStaUmI3R2d1UlFwYkxJTEsvcVRiUGdHcTNWMm1SYmV0cWNKa1BEVTZlOHBsTUQ1K2N3eWRUNHNUOVNlcE5saXhhUTJjTlh0QVBxVVJuYXFRQVhIbCtEWHQ0bDFtM2hSamlNY0pxVzJra0N6UHl2dmcvZ3g0MnBZeENZOThIQ0tudGFSZlIvYzNsWituMUxwR01DZmJVU3F1YXF1S0JQZHJmQzVxQmc4V2k5V3dzNTMvTERIODcxRE10VDkzWmx3OUxHMGhCNVd1VHVoc1VaYkZPTlFFaVREVFZzamVXNzlJYTlNYytZWU1nbWpnRndFQXZKYXBVK0hnR28zK0ZBVURkWUFieUNUeUFMNGhkcFpXR29yRGg5MDZWdFB0K0FGQk5GUEhjT3VBN1pnTkRZY1JkdE9nVEJsL2x6aTZqeURPOXp0NURCYTdodU5YaDRDZnBoR0R3a2duL2RrSHpwV09KWS94VFNRSnZpZ0VyWmFORWRaQjZXSlQzQ1RwaUtKR0pKcHFEV3BGTDcvdVhLQ1NqL0t5YkoybFVqaTVoVEQ5MURCbm5VazciLCJtYWMiOiIwMmU0MGYwYmQ3NTkzNDY2ZGViYWMyY2ZlMGVjNDU0MDVkYTgwNTkwOTM1ODZjNmFmYjRhZmNhYzFhMGE3OWJjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkFYTlFUYW5SR2pva2xqbkVjS1E1WUE9PSIsInZhbHVlIjoiVHcxZ0gvSk5sbDlLck4xZnJWRGphOHduUy83WFlQZmJhaTdWM0Z1UVh1a0VtMnVlWDU4Qk1rKy9FZTdabFVzRUdGYTBQQ0RmaDVzMlZiSWJBTGZBRTZYbTM5WVF0dmxmWkd0c3h2dzFDMStlWjRJOTJ4RUpWS1J2TktHdzRkNk5laUFXYURSbWRtL0h4bjNkVDJjbjJ2cVRzL0ovZUlrZS9RaVcwOGVxU1FwK0RST1NuM2RDRjFtdjRYMGo0T1pwazVncDFrZU03dVYwYVdRTWhBRkxKYTJpcGZrYm1FTTdNeDQvSSsvZ0pRZnhtT0Q5M3cwNFlkcHRxeDdkNWtuOVM2ZnZDTnExbjl5eFA4RkdHdmRDTk9wY2hGSmlhamtQZ25tZllFdDlOdFVSdFNuM3lqNFV3aER4ZExOeElWUXh1QVBPRWcra2RPMy96K1BuZnloWkxYeWdIVHBSR1dKV1BFYWVnRnR6NEpvK0xid0NiZEVMT1N3bXBQamw5aVRTc25KY1A4RWhkT0N2SHZuQkNnVExVNVRHRmZkeVNzY28vNHNjUWdyYWM2VFl6NWRIQmlGVGI3dkVKc1RxZDJMcXF6aURtNGRSVjBSaEJzN09aU1J0aFJnQ3JZUnNzZFk0ZHgyVmZCcW9WK0hwaVFPZUlETTFweUR2UG1HMG53Yk8iLCJtYWMiOiI4MTQ0ZWFmYWE5M2QzMmU0NmMxOTY4MmI4NGZjMGYxYWEwOGVlMTk4NDE0YzRiMzY3MDUyNDlmMmYyMzBlZjQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1613361381\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1667275763 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1667275763\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-306367300 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:16:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikl2Y3pUZlh3czlxWnVjN05ELzBqcVE9PSIsInZhbHVlIjoiYnpmTlZpb0R3TThTdXhhb2tvUEFFbVI1bmJYaU5UUlk5S1NjQitVUG1idTZKeDQ5Ly9YUDZCVkNOOG9oMkpUTlNyaGtRVXNreFJCSDhlL202a203aG9uTUo5bmErcHNDd3JmUStpUkx0UGNaZUlVdXpQM1hLRTVEYkp0WmhpampSZ0lCRFFnV1FkY2hmMjY0ZFVyM2NsYTVyS2VhQmZlNGJ6YlYyaE5LM2JVQk9DclZRR1d1bGg4N2pWY1ZHVnZsUzZIcVk5Q2Y2S1VEMWcvWEtrcFRxbnM1TDFpVE1BTDliR2w4ZWhBRzJQSkpSdE9KQlFIL2k3K1FCRlBpcUR1TWVNMzZLYmxJb0ZybEJZdldQa1gvUlVGV0V0d2xUOERtQkZCaXM3K3JqMU9EaVZWeVlDdWxhVXM2c21NaURxZUIwdXp6ZC96bGtiRG43L0R1R29MOU9FaEpkT1dYeDdkdmlKMG5kcDJYSkhOeXRmQi9ic0Y5MTlzdXp2VmFwZlFKaGxtMTdFMU9oVlNidG0zNXRJMHBPVWI2T0FiODRteGQzYTEwRTJzcDFsQzBqTTgzMG5NYm9rNERhWGpvakZ1VndTQjRGUWJiQWxxanlQOXJFcTFzdU1jaDNyampsdldFNUN2MkYvYjJMd1haamtZdG5hdUVRQURuUkh1STNnVVUiLCJtYWMiOiJlM2NlZGQ1YjdiMDgxYzM0NTk3OWMwMDg4MDIzMzYzYjYwZjRlZjE4NDkwMzU0YjE4OGFhYWQ4ZmQ4ZmEyNWQ3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:16:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImZmQlJRb1JqOS9ENzF3Q1c2Y3I2Rmc9PSIsInZhbHVlIjoiOUNEanIrWlJDZ2YwdDZ5WDc3RDlTcEo0TzZ1VWZYd0N0OWxuNjFEZkpkUlpDWjE2UW1hWnFDZkIzOENtWk82b01qbWV5b0I2b2ErZmdEK05jVjRuQnRvQzB0Qzd6SlB4NFczV2ZMRkF3cXBTcXg3UUdJSk9YM2JoNUExZUY1c04vaElZOVJ5T0RDM3pPbjRwNTF0dFZCdndMclpnbEcvd2tnS01OeUxxOXowWUxEd0lEcDNYOEkyYTVYaWhRekY0RUp1SDJjeFo2OE5SemlLV2FZNExkZTJIeFZHVUx3RGdKd1NTaEJteDZCR2hXTXI4ZWQ2RzBZS0JUOTZwNHpwbmJmZnNrS0F1Y3kxWEpRQStjVzVVeWs4OEl3RnBxTERNOCtIZ1piVlU2djBSckxqQm5qdGphVDh5b1FYUXlES1BKVlVENk82N1VuS25pNFYyQldIazMrbUg0K0lnWkJseDJKTVNvQUZxdm9QRkxZNnJLdFBRaTBYeUp1WldCRFA1d0RIV2d5WnVWNGZZMC94K0x1a1ZkUlIyWWpUTnpqeCs4TTdLWkNaRGpOc2s3MFIyYmJPUVQ5WWdSTmZGWDlRbDhtMXZQNEhncWNHQXIzZWxBZ1U0aUM4TzR1VDhweUViT3MxbkZ5OW1kT3BmNTQ3VXlWc0czaVVnQURqdUJ1RnciLCJtYWMiOiI2MDM5NDMzM2M3MTA2NzNjY2Q0MWM1MjFjNzQ2OGQ4MjY2Y2NmZDNlNjViYTQ1NjFlZDhkNDQwNzljM2Y4ZmViIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:16:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikl2Y3pUZlh3czlxWnVjN05ELzBqcVE9PSIsInZhbHVlIjoiYnpmTlZpb0R3TThTdXhhb2tvUEFFbVI1bmJYaU5UUlk5S1NjQitVUG1idTZKeDQ5Ly9YUDZCVkNOOG9oMkpUTlNyaGtRVXNreFJCSDhlL202a203aG9uTUo5bmErcHNDd3JmUStpUkx0UGNaZUlVdXpQM1hLRTVEYkp0WmhpampSZ0lCRFFnV1FkY2hmMjY0ZFVyM2NsYTVyS2VhQmZlNGJ6YlYyaE5LM2JVQk9DclZRR1d1bGg4N2pWY1ZHVnZsUzZIcVk5Q2Y2S1VEMWcvWEtrcFRxbnM1TDFpVE1BTDliR2w4ZWhBRzJQSkpSdE9KQlFIL2k3K1FCRlBpcUR1TWVNMzZLYmxJb0ZybEJZdldQa1gvUlVGV0V0d2xUOERtQkZCaXM3K3JqMU9EaVZWeVlDdWxhVXM2c21NaURxZUIwdXp6ZC96bGtiRG43L0R1R29MOU9FaEpkT1dYeDdkdmlKMG5kcDJYSkhOeXRmQi9ic0Y5MTlzdXp2VmFwZlFKaGxtMTdFMU9oVlNidG0zNXRJMHBPVWI2T0FiODRteGQzYTEwRTJzcDFsQzBqTTgzMG5NYm9rNERhWGpvakZ1VndTQjRGUWJiQWxxanlQOXJFcTFzdU1jaDNyampsdldFNUN2MkYvYjJMd1haamtZdG5hdUVRQURuUkh1STNnVVUiLCJtYWMiOiJlM2NlZGQ1YjdiMDgxYzM0NTk3OWMwMDg4MDIzMzYzYjYwZjRlZjE4NDkwMzU0YjE4OGFhYWQ4ZmQ4ZmEyNWQ3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:16:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImZmQlJRb1JqOS9ENzF3Q1c2Y3I2Rmc9PSIsInZhbHVlIjoiOUNEanIrWlJDZ2YwdDZ5WDc3RDlTcEo0TzZ1VWZYd0N0OWxuNjFEZkpkUlpDWjE2UW1hWnFDZkIzOENtWk82b01qbWV5b0I2b2ErZmdEK05jVjRuQnRvQzB0Qzd6SlB4NFczV2ZMRkF3cXBTcXg3UUdJSk9YM2JoNUExZUY1c04vaElZOVJ5T0RDM3pPbjRwNTF0dFZCdndMclpnbEcvd2tnS01OeUxxOXowWUxEd0lEcDNYOEkyYTVYaWhRekY0RUp1SDJjeFo2OE5SemlLV2FZNExkZTJIeFZHVUx3RGdKd1NTaEJteDZCR2hXTXI4ZWQ2RzBZS0JUOTZwNHpwbmJmZnNrS0F1Y3kxWEpRQStjVzVVeWs4OEl3RnBxTERNOCtIZ1piVlU2djBSckxqQm5qdGphVDh5b1FYUXlES1BKVlVENk82N1VuS25pNFYyQldIazMrbUg0K0lnWkJseDJKTVNvQUZxdm9QRkxZNnJLdFBRaTBYeUp1WldCRFA1d0RIV2d5WnVWNGZZMC94K0x1a1ZkUlIyWWpUTnpqeCs4TTdLWkNaRGpOc2s3MFIyYmJPUVQ5WWdSTmZGWDlRbDhtMXZQNEhncWNHQXIzZWxBZ1U0aUM4TzR1VDhweUViT3MxbkZ5OW1kT3BmNTQ3VXlWc0czaVVnQURqdUJ1RnciLCJtYWMiOiI2MDM5NDMzM2M3MTA2NzNjY2Q0MWM1MjFjNzQ2OGQ4MjY2Y2NmZDNlNjViYTQ1NjFlZDhkNDQwNzljM2Y4ZmViIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:16:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-306367300\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}