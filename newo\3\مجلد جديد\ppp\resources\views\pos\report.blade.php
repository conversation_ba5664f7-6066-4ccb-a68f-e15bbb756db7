@extends('layouts.admin')
@section('page-title')
    {{__('POS Summary')}}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('POS Summary')}}</li>
@endsection
@push('css-page')
    <link rel="stylesheet" href="{{ asset('css/datatable/buttons.dataTables.min.css') }}">
@endpush

@section('content')
    <div id="printableArea">
        <div class="row mt-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table datatable">
                                <thead>
                                <tr>
                                    <th>{{__('POS ID')}}</th>
                                    <th>{{ __('Date') }}</th>
                                    <th>{{ __('Customer') }}</th>
                                    <th>{{ __('Warehouse') }}</th>
                                    <th>{{ __('Sub Total') }}</th>
                                    <th>{{ __('Discount') }}</th>
                                    <th>{{ __('Total') }}</th>
                                    <th>{{ __('Payment Method') }}</th>
                                    <th>{{ __('منشئ الفاتورة') }} ({{ __('Invoice Creator') }})</th>
                                    <th>{{ __('الإجراءات') }} ({{ __('Actions') }})</th>
                                </tr>
                                </thead>

                                <tbody>
                                    {{-- @dd($posPayments); --}}
                                @forelse ($posPayments as $posPayment)
                                    <tr>
                                        <td class="Id">
                                            <a href="{{ route('pos.show',\Crypt::encrypt($posPayment->id)) }}" class="btn btn-outline-primary">
                                                {{ AUth::user()->posNumberFormat($posPayment->id) }}
                                            </a>
                                        </td>

                                        <td>{{ Auth::user()->dateFormat($posPayment->created_at)}}</td>
                                        @if($posPayment->customer_id == 0)
                                            <td class="">{{__('Walk-in Customer')}}</td>
                                        @else
                                            <td>{{ !empty($posPayment->customer) ? $posPayment->customer->name : '' }} </td>
                                        @endif
                                        <td>{{ !empty($posPayment->warehouse) ? $posPayment->warehouse->name : '' }} </td>
                                        <td>{{!empty($posPayment->posPayment)? \Auth::user()->priceFormat ($posPayment->posPayment->amount) :0}}</td>
                                        <td>{{!empty($posPayment->posPayment)? \Auth::user()->priceFormat($posPayment->posPayment->discount) :0}}</td>
                                        <td>{{!empty($posPayment->posPayment)? \Auth::user()->priceFormat($posPayment->posPayment->discount_amount) :0}}</td>
                                        <td>
                                            @if($posPayment->status_type == 'returned')
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('مرتجع بضاعة') }} ↩️</span>
                                            @elseif($posPayment->status_type == 'cancelled')
                                                <span class="badge bg-secondary text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('ملغية') }} ❌</span>
                                            @elseif($posPayment->delivery_status == 'delivery_pending')
                                                <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('جاري توصيل الطلب') }} 🚚</span>
                                            @elseif($posPayment->delivery_status == 'delivery_completed')
                                                <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('تم توصيل الطلب') }} ✅</span>
                                            @elseif(!empty($posPayment->customer) && strpos(strtolower($posPayment->customer->name), 'delivery') !== false)
                                                @if($posPayment->is_payment_set)
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('تم التحصيل من مندوب التوصيل') }} ✅</span>
                                                @else
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('جاري تحصيلها من مندوب التوصيل') }} 🚚</span>
                                                @endif
                                            @elseif(!empty($posPayment->posPayment))
                                                @php
                                                    $paymentType = $posPayment->posPayment->payment_type ?? 'cash';
                                                @endphp
                                                @if($paymentType == 'cash')
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('Cash') }} 💵</span>
                                                @elseif($paymentType == 'network')
                                                    <span class="badge bg-info text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('Network') }} 💳</span>
                                                @elseif($paymentType == 'split')
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('Split Payment') }} 💳 💵</span>
                                                @elseif($paymentType == 'pending')
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('Pending Payment') }} ⏳</span>
                                                @else
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('Paid') }} ✅</span>
                                                @endif
                                            @else
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('Unpaid') }} ❌</span>
                                            @endif
                                        </td>
                                        <td>
                                            {{ !empty($posPayment->createdBy) ? $posPayment->createdBy->name : __('غير معروف') . ' (' . __('Unknown') . ')' }}
                                        </td>
                                        <td class="Action">
                                            <div class="action-btn bg-info ms-2">
                                                <a href="{{ route('pos.show', \Crypt::encrypt($posPayment->id)) }}" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="{{ __('عرض التفاصيل') }}">
                                                    <i class="ti ti-eye text-white"></i>
                                                </a>
                                            </div>
                                            <div class="action-btn bg-success ms-2">
                                                <a href="{{ route('pos.thermal.print', $posPayment->id) }}" class="mx-3 btn btn-sm d-inline-flex align-items-center" target="_blank" data-bs-toggle="tooltip" title="{{ __('طباعة الفاتورة الحرارية') }}">
                                                    <i class="ti ti-printer text-white"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="10" class="text-center text-dark"><p>{{__('No Data Found')}}</p></td>
                                    </tr>
                                @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
    $(document).on('click', '.pay-button', function(e) {
        e.preventDefault();
        var id = $(this).data('id');

        if (confirm('{{ __("هل أنت متأكد من تحصيل هذه الفاتورة؟") }}')) {
            $.ajax({
                url: '{{ route("pos.collect.payment") }}',
                type: 'POST',
                data: {
                    id: id,
                    _token: '{{ csrf_token() }}'
                },
                success: function(data) {
                    if (data.success) {
                        show_toastr('Success', data.success, 'success');
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        show_toastr('Error', data.error, 'error');
                    }
                },
                error: function(data) {
                    show_toastr('Error', '{{ __("حدث خطأ أثناء معالجة الطلب") }}', 'error');
                }
            });
        }
    });
</script>
@endpush