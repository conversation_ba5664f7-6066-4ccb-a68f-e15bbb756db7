{"__meta": {"id": "X1b0a19101c0127ce62ad058bed996d05", "datetime": "2025-06-17 15:19:12", "utime": **********.51789, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173551.217061, "end": **********.517923, "duration": 1.3008620738983154, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": 1750173551.217061, "relative_start": 0, "end": **********.363423, "relative_end": **********.363423, "duration": 1.146362066268921, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.363444, "relative_start": 1.1463830471038818, "end": **********.517927, "relative_end": 3.814697265625e-06, "duration": 0.15448284149169922, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46106232, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01185, "accumulated_duration_str": "11.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.443375, "duration": 0.00912, "duration_str": "9.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 76.962}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.476548, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 76.962, "width_percent": 11.561}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.493732, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.523, "width_percent": 11.477}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6ImY0NWFJUy9KVVB0ZGc2WG1pcVBnYUE9PSIsInZhbHVlIjoiTTJ0U1lualJ1bkJRU1RvSlQ0ZU1Edz09IiwibWFjIjoiMjAzNzNmZjMwN2JjMzE5OWVjZWZlYjUzNmM5ZjA5ZjY0YTRiOTdlZGUwYzQwNmRhZDIxMWM0ZGQyYzlmMzkxOSIsInRhZyI6IiJ9\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1143228312 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1143228312\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-886079972 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-886079972\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1285339241 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1285339241\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImY0NWFJUy9KVVB0ZGc2WG1pcVBnYUE9PSIsInZhbHVlIjoiTTJ0U1lualJ1bkJRU1RvSlQ0ZU1Edz09IiwibWFjIjoiMjAzNzNmZjMwN2JjMzE5OWVjZWZlYjUzNmM5ZjA5ZjY0YTRiOTdlZGUwYzQwNmRhZDIxMWM0ZGQyYzlmMzkxOSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173379875%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im1uNmg1K0UxSEY4SW9uTWhkcWM2WUE9PSIsInZhbHVlIjoiczc4S1FnenkwZ2dtbG1QSjg0RHlvc1hWZENlNFc4RmpzdnpzUExEcW42NWxyM21na3phNnlYRnk1UFdKTGJTVTdRUHNsTlRzMlNabWR0b1FhWmlDSEpjNHIvRWdsUjMrQkpZUkZqYk94Q0FlTW5UOVBzMmllOUhlbkpzTDgvVndScjY5SkprM2drTjgvZHE5ZHlhWmp4cVp3T09yclNjNTluT2dJVFJHZzgzMmM5aUxxbmU5Z01jWE13ZEloMnJOUktvam9JYjkwZmlaN1oyR2hHcWgrdXlCZkJQeDVmbjdHYU5aQkpGSDJyVDFmcHQrb1AxT25uZ24wYmh4RE5OQ2JNTTNNNlkvT3h0R2tpaGQ5QmRQd1RoWjdZM1RYUWF0LzZYKzlmc05DQkhjMDFXTGczd21JZGFIVFcrbGEyd09rM1NoTHVQeHpQYi9LTGlod2x6ek1nQThiU2lmS1pTdDNHenBIYkh0ZGw4TDZmeXFQRGRnZUJVUlc2M25WbXRjUEQ3czhDbmV0bkV4L0FNL1lrYk9OQ0Z2L0pHb1dqbkQ5bEFUQ01mdUh1K2p4RWp2U3M0VFgveGk1cEtSZFBzZXVzbk1IaEQ5R0t3SHRNbWw4ZVFzOTkybTZuQWFKaE1Ma1R2OHUyT0UvMDVkTmlLUzJoS3U5b2VWazRNYUFKcW4iLCJtYWMiOiJkZTJhNmRiZWM0NzkwNmU0MGZiZjA1ZDQwNWUzMmQ5NDg5Y2FiNjYyOGQxNjcwNzI0ZWFkOWQxZGVjMGQxYTU0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJIUDQ0b0V2VkdhRzhQZ1ZIdmxPQUE9PSIsInZhbHVlIjoiVUorTVRwa0RuUXBKYmFJL1FWVFhvZDRJZHVzanRLa0w0Q2ZFQlcrdGN1SEUvVVZTQlZXWUFtYTFZK0NubzBncHJoYU9PbnkxZ1BYL1h2VWkvSFlrM0ROU1ZPRTFBaHlIay9wKzdWTmtrcjdoTE9pS1BEZllEM25VY3REa242bTJYbmdRY3RZSWhNTUNid3pLR2FTNFc4TjRzNkdUNUhaL0lTV2MyY1BUdEFqRXcwT2pSZllNd0h2MWNxOVJ1OHlMYWlDbS82dmVPa2h3Z21ybTh3b1NjVURsYVZDcWlhTGZyUnE2L1ZRR2pxTEVDSHRvL2s4RlJtUFRlMjBVU0hmdXZFUURBY2orZnlLa0VZeWpmZ2w4bS9FRDlpSHh3VGwyV0N5dEN4Ri9yTlpMZUhLaDdSWmFpSWFyeHk1bXVmRFEydUFDem1CTkZWKzE3RFlJYTUvdEdvTlFXTVh4aUtSN0trcVpkODRrMlFjVGc4bVVIb3pmWXFRMXRKN3lBSkZERCtVd3lJc1BybEE5eEx4eFNUZllHR1hTcGU1dmVxL1pqWnhsV01lL1FLc2NLTDc5cVdLWGdmMHNzVDAwVWlPMFljYW1kd2hOYzRsTDFFV1kxdS96blRQYis3aHdFdU45ZWhKTG9raVRnd0M2QUFqaVRCQk1jdkM0RmJ2cHhBMVciLCJtYWMiOiI1MTNmZmQ0Mzg3OGJkNTBkMTU5MzcxZDI4MDYyNTU5MmIxZTBhYmE2MWM3YzcxMjE3NmI5MTc3YzczMjE3YzcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:19:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBCYUxEN1FRQzFySGpuMHNIY25Ycnc9PSIsInZhbHVlIjoiL0lqMW9JeU4vUnpmS1pGaFpVeHZneklqSjlnTUhHZlF6cFhudFJ3aEFTZXZIUkZGbGNmeFFnQVhWV1hlSlFaUkUwK0trcU93ZFdPQlFEVTNMdjlQYW9vVGtCNEYzMGJLUHQxblJHdkdKRHYwOVJLbnNaSitOc1BPSUNZY1pRM0lxVVVveG1RT3FXNldzVDB3QVh3Ynduek00VVZ0Y2dIMGlsNjZNRHVKdmxYSTdaa0FRL3JwSTlkdmVsR0VycndSRElNWlFCRHhXMXArWk43ZlUyR3R4bENnOU1HZU93bTVQcG1VSVF6Y21OeWxlMEc2M0ZiVDRuOWpYMDlLN2huZ2dleE9zRUZCUitRS2VUT2RreFk3R0ZzN003dktNelVabnZLdkhtcGJkMERONFo5dzV5REsveHprT3ZidFVXbnNFT2tEWjVlUmh5eHVQaWd3VjgreWx1cDdMSWlIdUFCOVAwT2p5UjVkRnUrMzNwTlZuYUdtbVMwcUxLYWNUWGl2NVR2ZUdBZ3FsSWlCNjNCQmM4ZVBXTE9XK0ttL3c4aW4rWXRJeGRQVWVJM0hRamRtQS9nTWFWMmhybHI1aVZmU2ZYUXN4SytlVUtGbEdGUWM2UmRKeTRkdUFBeWdKc2ErRFg2b3gyODFyQnRmT0x2NENQNkF5WTR0N3RKdERhNlEiLCJtYWMiOiJmZjFiZTBkOTE4Y2E3OWY3ZmZjZGFjNGNiNGU5ZjM3ZWFiMzUwYjczZTkzMWNiNzU1NDRmZjRlYWJjZGI4YmM3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:19:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImxSQ0g3V1kwTEl2RUlsYjV3U3ljc0E9PSIsInZhbHVlIjoiUndnYnZYRk5uWmY0TlBodDVNTERYZmFaVzJEWVY0TkFMYzk5c3MyakhYbXZsR2xXZXpUd2JLNFg0SXczc0sxc1l1bXlPOXdlU28rOEhueUhGa2ZZMWFLTGR4bEVMSnA4SDVjS25MeFdIdm1DOVJRZEVEaXVBRWhSYUw1NGlza1MxajJJNWQ1TE1mZUxlRjErZjhYZEZZSWovNjlRNVdjK1BGMGp3QjMycmoxY1lTdlJ3d2JGR0dyNXl2bTFHYVJ0a2Z0VkF3SGt0ekZTSGpPU2l5MVNhZ0wySWZVWWJVVnlMdk1HaXhMMWs2a09QYnladEg0Q1NIdnVzYzU2Mk5xRlc5Nk1aY1lVRkYxcVBCb2hYY0UyWFUwbzJqYnV0Y01aKzM5ZUs1TUtsaUYwTElPdlRnNkMvR1FVS3BVTCtPS3hXcEFLSVV3NjhVTHN3TXJxS3pDU2d0UWxRMloyMFNPWk5xNU9HTmJ0REgzRngzNndVN28veXpIa0VYRDNhSUlrREx4M1lnRU8zdkN5Qy9CUzJnRzk5QUNoNUhrS0FHdEhyNlZGWGhOMXFyWlpjdGtvUkwxNkgzV21RNUwrQjRoYWp3V0pqOVRKMUd4Y080eURJaGhnRVRRZHJ1WXhGcWlzbWg1b3A2V0N2aml5aytWMFpjeDNUR3NWME9WdUJIWWoiLCJtYWMiOiI5OWIxYmMxNGMwMTgzY2M1N2QzNTUyYmEyM2ZjYjg1NzU2MDRlOWZkMTVmN2Q3YmQ0YWFhNWM0M2ZhMjU0ZTg5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:19:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBCYUxEN1FRQzFySGpuMHNIY25Ycnc9PSIsInZhbHVlIjoiL0lqMW9JeU4vUnpmS1pGaFpVeHZneklqSjlnTUhHZlF6cFhudFJ3aEFTZXZIUkZGbGNmeFFnQVhWV1hlSlFaUkUwK0trcU93ZFdPQlFEVTNMdjlQYW9vVGtCNEYzMGJLUHQxblJHdkdKRHYwOVJLbnNaSitOc1BPSUNZY1pRM0lxVVVveG1RT3FXNldzVDB3QVh3Ynduek00VVZ0Y2dIMGlsNjZNRHVKdmxYSTdaa0FRL3JwSTlkdmVsR0VycndSRElNWlFCRHhXMXArWk43ZlUyR3R4bENnOU1HZU93bTVQcG1VSVF6Y21OeWxlMEc2M0ZiVDRuOWpYMDlLN2huZ2dleE9zRUZCUitRS2VUT2RreFk3R0ZzN003dktNelVabnZLdkhtcGJkMERONFo5dzV5REsveHprT3ZidFVXbnNFT2tEWjVlUmh5eHVQaWd3VjgreWx1cDdMSWlIdUFCOVAwT2p5UjVkRnUrMzNwTlZuYUdtbVMwcUxLYWNUWGl2NVR2ZUdBZ3FsSWlCNjNCQmM4ZVBXTE9XK0ttL3c4aW4rWXRJeGRQVWVJM0hRamRtQS9nTWFWMmhybHI1aVZmU2ZYUXN4SytlVUtGbEdGUWM2UmRKeTRkdUFBeWdKc2ErRFg2b3gyODFyQnRmT0x2NENQNkF5WTR0N3RKdERhNlEiLCJtYWMiOiJmZjFiZTBkOTE4Y2E3OWY3ZmZjZGFjNGNiNGU5ZjM3ZWFiMzUwYjczZTkzMWNiNzU1NDRmZjRlYWJjZGI4YmM3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:19:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImxSQ0g3V1kwTEl2RUlsYjV3U3ljc0E9PSIsInZhbHVlIjoiUndnYnZYRk5uWmY0TlBodDVNTERYZmFaVzJEWVY0TkFMYzk5c3MyakhYbXZsR2xXZXpUd2JLNFg0SXczc0sxc1l1bXlPOXdlU28rOEhueUhGa2ZZMWFLTGR4bEVMSnA4SDVjS25MeFdIdm1DOVJRZEVEaXVBRWhSYUw1NGlza1MxajJJNWQ1TE1mZUxlRjErZjhYZEZZSWovNjlRNVdjK1BGMGp3QjMycmoxY1lTdlJ3d2JGR0dyNXl2bTFHYVJ0a2Z0VkF3SGt0ekZTSGpPU2l5MVNhZ0wySWZVWWJVVnlMdk1HaXhMMWs2a09QYnladEg0Q1NIdnVzYzU2Mk5xRlc5Nk1aY1lVRkYxcVBCb2hYY0UyWFUwbzJqYnV0Y01aKzM5ZUs1TUtsaUYwTElPdlRnNkMvR1FVS3BVTCtPS3hXcEFLSVV3NjhVTHN3TXJxS3pDU2d0UWxRMloyMFNPWk5xNU9HTmJ0REgzRngzNndVN28veXpIa0VYRDNhSUlrREx4M1lnRU8zdkN5Qy9CUzJnRzk5QUNoNUhrS0FHdEhyNlZGWGhOMXFyWlpjdGtvUkwxNkgzV21RNUwrQjRoYWp3V0pqOVRKMUd4Y080eURJaGhnRVRRZHJ1WXhGcWlzbWg1b3A2V0N2aml5aytWMFpjeDNUR3NWME9WdUJIWWoiLCJtYWMiOiI5OWIxYmMxNGMwMTgzY2M1N2QzNTUyYmEyM2ZjYjg1NzU2MDRlOWZkMTVmN2Q3YmQ0YWFhNWM0M2ZhMjU0ZTg5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:19:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-846991896 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImY0NWFJUy9KVVB0ZGc2WG1pcVBnYUE9PSIsInZhbHVlIjoiTTJ0U1lualJ1bkJRU1RvSlQ0ZU1Edz09IiwibWFjIjoiMjAzNzNmZjMwN2JjMzE5OWVjZWZlYjUzNmM5ZjA5ZjY0YTRiOTdlZGUwYzQwNmRhZDIxMWM0ZGQyYzlmMzkxOSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-846991896\", {\"maxDepth\":0})</script>\n"}}