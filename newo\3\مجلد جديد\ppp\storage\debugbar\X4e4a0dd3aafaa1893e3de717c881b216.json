{"__meta": {"id": "X4e4a0dd3aafaa1893e3de717c881b216", "datetime": "2025-06-17 15:32:01", "utime": **********.265109, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174319.839608, "end": **********.26515, "duration": 1.4255421161651611, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1750174319.839608, "relative_start": 0, "end": **********.088405, "relative_end": **********.088405, "duration": 1.2487969398498535, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.088425, "relative_start": 1.248816967010498, "end": **********.265155, "relative_end": 5.0067901611328125e-06, "duration": 0.17673015594482422, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46105656, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00881, "accumulated_duration_str": "8.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.180615, "duration": 0.0056500000000000005, "duration_str": "5.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.132}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.21494, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.132, "width_percent": 15.21}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.235431, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 79.342, "width_percent": 20.658}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1400083202 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173975623%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlkT0N1OTRIbm13bUNYczhpa0xJcGc9PSIsInZhbHVlIjoiWnNCb3ZJdVdacVprbmJ3cDg3eVRyYWZIM3Y2ZVNYUzM0NjBUanJzTkJDc25jTXZUS3hsbnphYWFvVWxMdHRteHlTTVFLRS96cXVUYnVOMVpUeGNWMkNOQ0krNDZrdExtUWUzZ1F5ejdLb1E2citDMkMyYko0bnVNU1hPdjNRZkpQWkN6SHRRVUViSjZudzVhODE4U3hGMHIyaXpGdkxlWjVHbkdyOXJodS9tVTErNTI4UzlqVXlGLzhvVkNlN0NHTjNMRC9jQVNobHpkN012enVzQk5yQmNWeXJUUk5IcEdWLzJUb2ZPcXhqQmR0a3NidWtUOGtteXI5dEgxTDNYUHhZVGI3b1p1TURqc2JwZjA5Y0lhWS9uUEs5dTVsVUcxaVRUbHNJTzRjS29yZHNacE9OYkFSNWRqTWtaemNqSVcvSmwvdGtMdHBGVzdPQ3UyZE9TR0FyWFphakgwaXVqaWcyOXMyeEVrSXRiRTJMSTFuRHQ2bDBxdEhxQWhXWFE2VFdxRUw2eHRQSGNKeGJJbmlzbGlxalkwNVovT3k4RytxYWQzNys0dnNzQ040SFozUEFQNjE3dkRvR085ZWJ4eWtCSmtiNUJFQmZqcTJuUTJ0Y012VXk3MDI0MkozejBmVElIVjdDb1BNNWF6RkQ4TTFHU3V3MDV6QjUwc1RjRCsiLCJtYWMiOiJjMGZiNWU2NWYwY2QzYmNkNmNkMjcyOWNiODAzZDk1Yjk0OGZlNTdhYWFlM2IyN2FmZWFlNzgxMGEyZTgzNzQ3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InBDTXJTcGhTZUppWGlGdCswMHZvdFE9PSIsInZhbHVlIjoiRkQrVmV2MlZ5NlBITG9JdDJqVGxRM2ZpRnNIb2lUa2lJRWkzOUlJNTQxSVJsb0Jkd3UxTkMyb2IydUN1Yms5VjNRV3U1ZWlDR3dmaEdqa0U5QUJLM3pZdHhyV0Y3Z3lHd1ZHcGZpNFVVS2QvdGJnMWp3dURtUUNEQVF5dTZ6YUJqa3ZHRnFGV1JidHR3WTFkS0MyQ2xObEI0cHVoVWkrcUorNlVRZlg1ZUk1K3NiU2hMYXB0ZFUyeVpBZVp4Q0hPMktVZHhVT3ZRRENBdGhLTlBPdVo2ZE0yUnMrbWJkRXdrOU5JUUNqSGwvNi9yRWtZdFRaRG5rM2ZvUGtRL283NUU2d1RmK01vMkxzYmdvQjBDaUdERjgxQXgxNHcvQngwVzhIM3ViRVlORTVpMjVQL3N4Wm93eFFBbXVCcGdBL2pkb3RLbWE3cjlFRW1GREcyMUFyTkVZRlhpN0kvMzF2ejJ0WUJ1S2dMMjQ2Um1pVzBoT090Si9mWE1VYjVBV1YyemFSdWhjY0RrcWxtUDdLWFdJNW1xYkJKMEZKOTArbHpKRlJicWhCakJsQXB5czBOV2Rjc0tjVHlpbWdIUVBOa2RJZml2SVJiVHM1bWZVSEtpcFQxaTNPclNXa3hJWllKdVZQd2ZlbFBpb2pscmlTOENuaXpPazIyOGtsZnc4RDgiLCJtYWMiOiIwMmFhMjdiZDg0ZjE1NTkwNTA5M2E2ZDQ1ODhlYWE1ZTc2NmJlNzkxYzNiMjJlMWJmM2YxYzA5ZGQ2NWNlODBmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400083202\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-537442112 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-537442112\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1682563024 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:32:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVXM3o3bEtBMGxETXZTUmRmdnMrSXc9PSIsInZhbHVlIjoiejNvelNPL0hIbnQvQWR2eVpaL0s5b2g1dGNWdVN2YzA5U3VvSnpNbnNnK1pjZWpzSlJ5akJueHppQ1A2L1Rheis3S0Q0UFI0UHU5cUZQelR5YkVjTS9SL3pKMzBvVVRwZCtpOXY5SkpHVEZPN1A4UUdPenRiM2tobkJzb2dGVmNYWWg5T2Q3a0g1THJMd052dzFWVXN2VVIxVnRCMXlYRzhFUS9YV3NWS2JQVTVQN21yekxLSGRPVXE3d0F0SUlJenE5NTVHWUs1S0hWenhwd2x6Zmo0dGh5WkJ2YVpkUUUzSW1LRExrSWZ4ZjNHdmppTzdlZ2g4a2l4NmMzdEk4R216dDNKOHVibnBCWTNRVUlnR0paY09wdjBsM1RnWngwbzZKMkltOTVVWlg5ZGdpaDhmdldMSmdRWnYyQ1lBWGVQRThIVFFnd0MrMTk4c3FBYmtBa1Eycm5JbTVYZXhUZ1pGaHJROVJMMnkyZzdwS3h5aHdvU1F2UVk1RG5rc0R0cDliRllvWFBMR2VpNDNMVUd6YnN1R1ZHUTR4OXU4RWJ6NnZaYW1XN3N1Sk1DdlpCTXpZNndhaHpZVkZtNGh5eUJtVDNMOGhUbC8zdEp2eEVNWjVmRkVoU0V2KzhNZ1pvVGhYTGxma3B1eTNGbUplaGlYQVlXSkJ3QmxxdHk4ajkiLCJtYWMiOiIzNTRlOWNiOWE2MzYzNjMyZjY0OGMxNWZhYTIyNzdkODEyODRkZTA1ZmM2OTY3YmZmZWYxMGE2YWVmODFhZjgyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:32:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImxadFVWT2NqWkkvTC9tTjJoallST3c9PSIsInZhbHVlIjoiMDJCa2Z2QmJsU3RTUms2YTByb090cVRLYTVXOHRySnltZHdpN1ZKcU5CZ3MvUnFlakZ3RXE0YVNqMmlxUmlndUdVVDVCalY2SUdxR2JyWm5jU0lSM3J2NkV2Wk13YnpzaU1BekF2b0lja0F1dnJ1YmRjN292RzdOYjBkWVNFK1A3YkdaLzZlNnlWQlZ4N1J1azRZUDhSYXkrSnVGZ0czKzBLMkJaUUR0amN6MEZoemplNEdQWDkvT282YVZZc01LTU5VSXlSeERCU09aNkRPR2xSc2tUbjYxNSs3L1dNT1p3Q2VaNjhsc0xZRkRJbHZyYnRYYnJwSDJTZ3VSdWUyaTNlMTZYTXlMZCtuQXRKQ3FncUhjY2owbWpZSGZGTTdnL1I4YWlsTURVNUJST3ZBc0UwaVIwS09VdDhDVUxNOGtTME1tVHlUQ3FWeU5LcW9NdS9KTmZGaHpCMzBNa2w0V3cvRnh6STg3YXFvT05seGxWWUlPMkdpdWgzWndRWG9PbGVGTWk4K0xaNGhPY0d4T08zaktnNEtvS2dSbkx0S1FnU1U4MEM4Uzd1SENMZWlqb2Vnbk5zb0RxOGhCRURhL1RmaGxtWDJPV3diSnB6aGZXdEt0SFVSQ0hQSjMyS3ROUGY2djM0ODhQS0FSS0xnaThTNG1zMmF2SzBTOHMxcEMiLCJtYWMiOiJlOWQ0OGQ4ZDAxMzAyZmEwODUzNjU2ODUzNzdkNDZlMTM5Njg3NGFiOWU5ZjFmZWEwMjdlNjZiZWJkNjhjZTQ1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:32:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVXM3o3bEtBMGxETXZTUmRmdnMrSXc9PSIsInZhbHVlIjoiejNvelNPL0hIbnQvQWR2eVpaL0s5b2g1dGNWdVN2YzA5U3VvSnpNbnNnK1pjZWpzSlJ5akJueHppQ1A2L1Rheis3S0Q0UFI0UHU5cUZQelR5YkVjTS9SL3pKMzBvVVRwZCtpOXY5SkpHVEZPN1A4UUdPenRiM2tobkJzb2dGVmNYWWg5T2Q3a0g1THJMd052dzFWVXN2VVIxVnRCMXlYRzhFUS9YV3NWS2JQVTVQN21yekxLSGRPVXE3d0F0SUlJenE5NTVHWUs1S0hWenhwd2x6Zmo0dGh5WkJ2YVpkUUUzSW1LRExrSWZ4ZjNHdmppTzdlZ2g4a2l4NmMzdEk4R216dDNKOHVibnBCWTNRVUlnR0paY09wdjBsM1RnWngwbzZKMkltOTVVWlg5ZGdpaDhmdldMSmdRWnYyQ1lBWGVQRThIVFFnd0MrMTk4c3FBYmtBa1Eycm5JbTVYZXhUZ1pGaHJROVJMMnkyZzdwS3h5aHdvU1F2UVk1RG5rc0R0cDliRllvWFBMR2VpNDNMVUd6YnN1R1ZHUTR4OXU4RWJ6NnZaYW1XN3N1Sk1DdlpCTXpZNndhaHpZVkZtNGh5eUJtVDNMOGhUbC8zdEp2eEVNWjVmRkVoU0V2KzhNZ1pvVGhYTGxma3B1eTNGbUplaGlYQVlXSkJ3QmxxdHk4ajkiLCJtYWMiOiIzNTRlOWNiOWE2MzYzNjMyZjY0OGMxNWZhYTIyNzdkODEyODRkZTA1ZmM2OTY3YmZmZWYxMGE2YWVmODFhZjgyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:32:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImxadFVWT2NqWkkvTC9tTjJoallST3c9PSIsInZhbHVlIjoiMDJCa2Z2QmJsU3RTUms2YTByb090cVRLYTVXOHRySnltZHdpN1ZKcU5CZ3MvUnFlakZ3RXE0YVNqMmlxUmlndUdVVDVCalY2SUdxR2JyWm5jU0lSM3J2NkV2Wk13YnpzaU1BekF2b0lja0F1dnJ1YmRjN292RzdOYjBkWVNFK1A3YkdaLzZlNnlWQlZ4N1J1azRZUDhSYXkrSnVGZ0czKzBLMkJaUUR0amN6MEZoemplNEdQWDkvT282YVZZc01LTU5VSXlSeERCU09aNkRPR2xSc2tUbjYxNSs3L1dNT1p3Q2VaNjhsc0xZRkRJbHZyYnRYYnJwSDJTZ3VSdWUyaTNlMTZYTXlMZCtuQXRKQ3FncUhjY2owbWpZSGZGTTdnL1I4YWlsTURVNUJST3ZBc0UwaVIwS09VdDhDVUxNOGtTME1tVHlUQ3FWeU5LcW9NdS9KTmZGaHpCMzBNa2w0V3cvRnh6STg3YXFvT05seGxWWUlPMkdpdWgzWndRWG9PbGVGTWk4K0xaNGhPY0d4T08zaktnNEtvS2dSbkx0S1FnU1U4MEM4Uzd1SENMZWlqb2Vnbk5zb0RxOGhCRURhL1RmaGxtWDJPV3diSnB6aGZXdEt0SFVSQ0hQSjMyS3ROUGY2djM0ODhQS0FSS0xnaThTNG1zMmF2SzBTOHMxcEMiLCJtYWMiOiJlOWQ0OGQ4ZDAxMzAyZmEwODUzNjU2ODUzNzdkNDZlMTM5Njg3NGFiOWU5ZjFmZWEwMjdlNjZiZWJkNjhjZTQ1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:32:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1682563024\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}