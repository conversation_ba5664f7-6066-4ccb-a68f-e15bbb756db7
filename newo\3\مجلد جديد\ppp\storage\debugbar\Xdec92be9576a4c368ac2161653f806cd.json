{"__meta": {"id": "Xdec92be9576a4c368ac2161653f806cd", "datetime": "2025-06-17 15:31:33", "utime": **********.447664, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174291.693239, "end": **********.447696, "duration": 1.7544569969177246, "duration_str": "1.75s", "measures": [{"label": "Booting", "start": 1750174291.693239, "relative_start": 0, "end": **********.193146, "relative_end": **********.193146, "duration": 1.4999070167541504, "duration_str": "1.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.193179, "relative_start": 1.4999399185180664, "end": **********.4477, "relative_end": 4.0531158447265625e-06, "duration": 0.25452113151550293, "duration_str": "255ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46219408, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03435, "accumulated_duration_str": "34.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.34679, "duration": 0.03143, "duration_str": "31.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.499}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4115648, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.499, "width_percent": 4.309}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.422603, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 95.808, "width_percent": 4.192}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1924991956 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1924991956\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-17262997 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-17262997\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1141797933 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1141797933\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1315424314 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173975623%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpZOFNseGE2VTA2VnhiekN3bjF6cEE9PSIsInZhbHVlIjoiUm93czZEK3FVa0REaHlMQ1A3NDFOOXQ2aXMrZnVQNWlOTkpSNnhxK3JLcTRKS3IvcUwwRTM5RmhVanczalpEei9qc21GMXgveW90S1pKcXRoUmhvVndObkIwS3ZrVEp0LzQ1dzQxTGZ5SHZFYWxyWXErdHRvVk85UmJ3RVJXUjJjcjg0d09iWlBaYmpOd251cEdtdDA4cmh0NWtGMW1ieDlSNFF1ZHg5ajNjbVdvRFpVVjhRaCtvWkhFOEpRVEpEQytEVnpPSmJtem8rKy84MEhXUlVFRjkyVTlYdk5JTWZMNy9DRlBqNEdGVlpkbWFvUVBoeXN1UGVsZ2xyWWV2MWpRNUlqVkw2cnBtVFlzWkQ1M2RFb2hZK2xhUmVWTERuRm1KRDNRYXBucHhnai9SWS9HZ0hpQyt5VEJwSWpxUmhBV1FQQWNmd2VDRDhUcVJUVE14NmpodDF3dlZQZDJFTmIzZC81UGdvcWZsNjg3QTlqckZzWnFjd0JvVVAzSlpONnFseElta1hkWS9NVnVFWVYvbmViWkpDNlpGTUFWYmk2M2NuTWoxOEt6V0pPSUhPUGU4QWVwaHZsclBqMER3dTRzQWFwK0o2UGk4RkdRcFFhbFNKWTB1b3VjbCtBM0lDZnNvRWZHUFg0WEFLYUVkQnVMYmRrU05sVms3MVFKZnUiLCJtYWMiOiI5ZjM4OTMwMmE1OTcwODJlYmY1MTMxMjUyODFiODNkZTdjNGFlYTZkZjQxNGZjYTdmMWUyZTlmZDNiNDUwYmE3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlJvcDJsVmJoTURZb3dZTFdEOUMzZUE9PSIsInZhbHVlIjoiS3FnUWczS2RTWldPcTgwRWRnc3lqeUExK1BEaWJsUzJka1BIRG5nTUYxWW83Y0c3Q0NnVWZ1Q29tNVMxUlNTSkZLaEhZTkxROVRTQ1l5cHdnenZ5VFc1WlA1aTNaM0JnQUVvSXhKaXhVa1BTMHpZWE1xV2krRk8xZXNvV3VISmU0N1M4cDVjVW1hbTUyNVVsOXRob3p1YzFsUHdTb1BmWmh2Mjl6OUhiUUNKYUFEOGxIbzhkNjNsSXhpdEFqMzZTbDNCQmcwUW1tNXowRUs3bEl0R2FOTUYrVUJtOTRQRUJKbkxtSk1NeW5WempEQVNzcnFKMTIvMnR1VHgzMlg1Sjl0dDh1d3lWV013S093UUx2cE5LbEpYU0JlUVJ3Vis0MTY4Qnd4NUcyRk1XVmtXM3Bhb3RXNXlKbmtIZGI3cTlUQyt2azk1a001dGU1cmJSandZUXJuc1gwcGMzT1ZROEkyc2k0aDBKcFNrbUNjdmJYNVdJc25qV1RGSDVJejZQWC9INkZTdXNEU1BaYVdwd1hyTXlDRURKNllBWkVRV28wOHdLMjFpUWtOdGN2ZEFleTZuSS9iOE9ObHdUa2ZwRzdYbklRbmhHNThaSXh1Y1JXcnZyVC9pbExjVmVDZndDRDZPRmI2TkxnODhGckNCb0x6dmRINUEwU2lXS05HZzIiLCJtYWMiOiJkNjJhMGUxZDZkYTBkYzMwNjc1ZDc2MzE4NjVjMzAxYmRkNjRmYTNiYzBiZDE3M2M3YmY4ZDNjMjU5YTUzYjhiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1315424314\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1157969856 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1157969856\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-615128749 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:31:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlkzMlVVSm96bzQzSW1zZ2tzUTlENXc9PSIsInZhbHVlIjoiak1jcWZEbnJzalp0cWZucjdxRHo4YzV1bWhuYjRVS2VvMnpoWGxjWDFkZ1UzdVR1Vk1EUWU5RWxmSHB5emdHLzZrTFdFUVNabHRpUjlmOTRMN1J1ZC9qVHE3b09WNlhSaXoyMkNVR1c0ck8zdXVGT2VCdTZCWXM0SFlCQWpWRHYrZktzVSs1cENHaVorYXV1eVZZaXZVeDljd1hzd2pzWVdnMjlZTXBYdXRTTlY5WFAyMFExWklpdmk3Z3l2RGZyOFIrTnArUGtwNXBwM1BkS3l2QUVack9nY0gzd2R1QXN0ZFB5OEZmd1ZMOU52Yng3bloxWUhqL1BhYTlFREZzRGZjWHlCd0VYWGRUZkNkUU5DcjBiVWM2b3pxSHpYM0dIWDdmWDlPby96QmxyZ1lrZEhjcnJRa0Fodmp0anV5T1VRU2dHcUhLK2oxaVJybnZsczRnVlllUjdpYklVV0s4WkQ0NDlZd0tOM09wNXhBWnVKZllsSmlqaURLWDBORnh4WnJ3c0EyYzhSb0pIOWQ1S1RjdTFCMU9LRFYySFFzR0NVT3d3VGpUWFBwR25YWlgzTC9DMWZBNTZTT2RFV29acUFvR0UwVE50eGZUSkJhY2RhM3B5Y01yTC92VENRSU5OZFlCek4xMVp3K0tNM3RQKzBmQkJFd1FQUE1WUUV4OVMiLCJtYWMiOiIzYTA0YjU3YTkxNGQxYWYyZmYyNjU0NDcxY2JiOWM3OGZkZjJhMjJkZDNmM2ZkOTc2NjI4MTk1MjY5ZWE1OWE4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:31:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjdpbmgzKzdZU2J4cVpTWmlNVE1lQVE9PSIsInZhbHVlIjoiYmk4RC94d1MzNWxFUzVCOUJrdVhpQk4yRFNtem0xTXZlaFJLaElIbGh3SVFUQkx5YXdJMFZ4WGR0VTBwTUh0S3hMMkFWenFXVkw3QytRMzhjUWhqejExZzJZL0s3ejZPNXZENzVkM1FMSkZtVHQ4MVB5dE5aR011Nys4c28rdzEwZDBqSUVpeEJKZ1Rocm00bjlWUEd6SVRIOHNyTWVlcnBHOVQxdGlLTzJxbFFzcytNdTNDeWZvUHBpdkNFRUNseDJ1TWU4U0ZvNTdDRCs2NVEvdS9pZDI2Z25qTmRaNWx1SHY1SUVlYUpoM3UrL25BZmViNGM5MWZIekp1L3dsM2FWeWNTZ0pWRVM4SjhrTVhmSVNEdjdWNmNFcHF2VUdsMmoxMFYrQlVtZW9nTmc1Q1ErRktybjdmcW9TbTZueUo3dndKcWtFVUlvY2NmbnpsQWtEbzUxZG45bnhTSTFWZjJSRDh3ZG9RbEhBa1p0KzdKL2g0dHdBTDhxUmhXTjArZU1ZWm1uQk5UNjlZb0oybE90TDhZUmM4bnBOSDFoOWtpN3FoMmM2WG9KSmgvSEFRdm1pWUp3aFlnekRBb3FNR2dHay9HVWpwQUZmRUJITVl6NTZEdFM1Z1FYZk5BZE1PTlR1eG9lOVlsSG5zTHNFdCtoeTlHNVRVTmgyRTdTQ04iLCJtYWMiOiJmOGRmYzYwNzc2YWFmNzE4OGI2YzhmYzk4Zjc2MTFkYTU4YmY4MDZjNDBhM2QxYmUzNDk0M2M1NmU3MjQwYzc0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:31:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlkzMlVVSm96bzQzSW1zZ2tzUTlENXc9PSIsInZhbHVlIjoiak1jcWZEbnJzalp0cWZucjdxRHo4YzV1bWhuYjRVS2VvMnpoWGxjWDFkZ1UzdVR1Vk1EUWU5RWxmSHB5emdHLzZrTFdFUVNabHRpUjlmOTRMN1J1ZC9qVHE3b09WNlhSaXoyMkNVR1c0ck8zdXVGT2VCdTZCWXM0SFlCQWpWRHYrZktzVSs1cENHaVorYXV1eVZZaXZVeDljd1hzd2pzWVdnMjlZTXBYdXRTTlY5WFAyMFExWklpdmk3Z3l2RGZyOFIrTnArUGtwNXBwM1BkS3l2QUVack9nY0gzd2R1QXN0ZFB5OEZmd1ZMOU52Yng3bloxWUhqL1BhYTlFREZzRGZjWHlCd0VYWGRUZkNkUU5DcjBiVWM2b3pxSHpYM0dIWDdmWDlPby96QmxyZ1lrZEhjcnJRa0Fodmp0anV5T1VRU2dHcUhLK2oxaVJybnZsczRnVlllUjdpYklVV0s4WkQ0NDlZd0tOM09wNXhBWnVKZllsSmlqaURLWDBORnh4WnJ3c0EyYzhSb0pIOWQ1S1RjdTFCMU9LRFYySFFzR0NVT3d3VGpUWFBwR25YWlgzTC9DMWZBNTZTT2RFV29acUFvR0UwVE50eGZUSkJhY2RhM3B5Y01yTC92VENRSU5OZFlCek4xMVp3K0tNM3RQKzBmQkJFd1FQUE1WUUV4OVMiLCJtYWMiOiIzYTA0YjU3YTkxNGQxYWYyZmYyNjU0NDcxY2JiOWM3OGZkZjJhMjJkZDNmM2ZkOTc2NjI4MTk1MjY5ZWE1OWE4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:31:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjdpbmgzKzdZU2J4cVpTWmlNVE1lQVE9PSIsInZhbHVlIjoiYmk4RC94d1MzNWxFUzVCOUJrdVhpQk4yRFNtem0xTXZlaFJLaElIbGh3SVFUQkx5YXdJMFZ4WGR0VTBwTUh0S3hMMkFWenFXVkw3QytRMzhjUWhqejExZzJZL0s3ejZPNXZENzVkM1FMSkZtVHQ4MVB5dE5aR011Nys4c28rdzEwZDBqSUVpeEJKZ1Rocm00bjlWUEd6SVRIOHNyTWVlcnBHOVQxdGlLTzJxbFFzcytNdTNDeWZvUHBpdkNFRUNseDJ1TWU4U0ZvNTdDRCs2NVEvdS9pZDI2Z25qTmRaNWx1SHY1SUVlYUpoM3UrL25BZmViNGM5MWZIekp1L3dsM2FWeWNTZ0pWRVM4SjhrTVhmSVNEdjdWNmNFcHF2VUdsMmoxMFYrQlVtZW9nTmc1Q1ErRktybjdmcW9TbTZueUo3dndKcWtFVUlvY2NmbnpsQWtEbzUxZG45bnhTSTFWZjJSRDh3ZG9RbEhBa1p0KzdKL2g0dHdBTDhxUmhXTjArZU1ZWm1uQk5UNjlZb0oybE90TDhZUmM4bnBOSDFoOWtpN3FoMmM2WG9KSmgvSEFRdm1pWUp3aFlnekRBb3FNR2dHay9HVWpwQUZmRUJITVl6NTZEdFM1Z1FYZk5BZE1PTlR1eG9lOVlsSG5zTHNFdCtoeTlHNVRVTmgyRTdTQ04iLCJtYWMiOiJmOGRmYzYwNzc2YWFmNzE4OGI2YzhmYzk4Zjc2MTFkYTU4YmY4MDZjNDBhM2QxYmUzNDk0M2M1NmU3MjQwYzc0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:31:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-615128749\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-970316556 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-970316556\", {\"maxDepth\":0})</script>\n"}}