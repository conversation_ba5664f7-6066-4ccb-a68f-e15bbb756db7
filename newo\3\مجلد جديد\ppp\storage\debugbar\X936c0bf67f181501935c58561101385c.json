{"__meta": {"id": "X936c0bf67f181501935c58561101385c", "datetime": "2025-06-17 15:25:55", "utime": **********.622568, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173954.339762, "end": **********.622599, "duration": 1.2828369140625, "duration_str": "1.28s", "measures": [{"label": "Booting", "start": 1750173954.339762, "relative_start": 0, "end": **********.463022, "relative_end": **********.463022, "duration": 1.1232600212097168, "duration_str": "1.12s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.463042, "relative_start": 1.1232800483703613, "end": **********.622602, "relative_end": 3.0994415283203125e-06, "duration": 0.159559965133667, "duration_str": "160ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46235848, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021830000000000002, "accumulated_duration_str": "21.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.546448, "duration": 0.01925, "duration_str": "19.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.181}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.591642, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.181, "width_percent": 6.551}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6024432, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 94.732, "width_percent": 5.268}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  8 => array:9 [\n    \"name\" => \"tea-شاهي\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"8\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1639187026 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1639187026\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1044579813 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1044579813\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-529770066 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173924302%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im1PRXZ2Ry9TckRleSs3WjM0WU1yN1E9PSIsInZhbHVlIjoicThFbDBzbGNkR2Z4QWc5dkdzaDI4enh1eVVEMWZUbjRCenRZS3JFZTkxTE9WK01IQzBZWm9rbDQwYmFwVDBoblRjcHU2elNwajB4VlRJVnhqU2FMT0JpQmZScVJOYWRNa0xUNWxUdmRrdEEyTkozS0RrUFlrQnB5cFRaY1IrU0xQOTEyVWxIVmJ2SDdUcXA5Q3M4a1l5a0NobjM0ZmQycStTNTVkaTFra3ZZbDZzQjF6SzQwWndwamN6OUdtejhNUWVTVzgxU05STkIzaU1BYVZIRTczNnVjeDdHZ2RkZGxrTjhZOTVzRW1xRkl2aUMyaU55VDVCa1pUaTRjdVZ2U2p0YVZSbmZKNTlCeEIzd0xWWWxCaDhQOFRLVHBHOW51V2Fldlp4ektFM0ZSQVNLR2g4anZWMFVTOXRxTXYwQWV5UXdwTU9sMlMrR0VFck5jZWR3MGp3WFlaMlhoQW5ELzdkcmdNR3FwRUlUdnlJTjJwQVBGT2ovdk5PK0M1MC9VOGxnNUt1WWV2MkQ1NUluUUpQU2Rhd0sxSjUzcjJPVXV2TSt4M3F4Mnp0M3Q2UTNsZ2UvbzdRZVcyTno2WFZya0xFU2xnTmJaRG9TNVgxU3dxVHVVK1ZiOUNvREI3ZG5SNEJJMHVuTUQ0c2NJVys0QkRhd3g5NWZWaEp2RGN5USsiLCJtYWMiOiIwYzQwNDQ5ZTU2OGEzNzExN2QwZjc1ZWQxMDU4ZDkxNjZkNWI5MGVkOTIwNjRlYTVjYWU3Yjg5MmJmODFiMGQ5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjJScm83WVhteEZJWHJpSzA4TUNEOWc9PSIsInZhbHVlIjoib2lWeExmR3h2M1FTTjRwRFRhejhHUnJ5Z2NJbmppdFhaSnhHRFh2c3k1d1hTczNaeVJqT0R4ZGpmSWd0cjZiOEduZm9LNnBSTkRVb1pYOHMxbU9jdGpndlFGWTZDREtqZHZmKzdVd2doS3VJS1BJL1huUXFSM0RtQ01ReUxMYUl2YmFJSThiK3FMaGQybEJRYzI4ZHdJU1FlNWNVdEE4dEVuT0JLMFdBQ29RNnBMN3lWWi8zMU44MXo2ZFdhVzVkUDJ0azgwd29jcmF3M0FoS0xyZWhPRTJXWXBNUnZUcGErZ2VEZ0QwNW04UTkrTnI1TEs5T0dKSitlQnI2N2NnbUZPOUtWb0pqYUJZaXMrQlJGK01sdmEwN0tMYzVoVE81MUhTN3NONXJTVEFXTmlvRzIzMGttVWpoSUJ6T0M4ZzF1TlkzUzA4NTRMSE42KzJtTXYzQUtmaS9XNHlGb1A0dHRoL3dHNWEwVWNCNzZVb0g0dzdqTEJQcmJNdTF3d1pnR0QxdC9ycjVaVW13ZFNMV0Mva0phWVc0M093cnVLSjR4R05BWnFGQWRSaG1XUFM3Z0JOQ2pBME5FOS9LcUc4SElTcHQ0TExXeEM2d3k1UjkvZjd3am5MYmIyWHYwNzVTZFBZWENZTXdOTDJNUC8wQ1U2K0JvQmpNcTYvSUp4QnUiLCJtYWMiOiJmZjQwZThmNTkzYTE1OTA4OWFhNDBhOWU2YTQ5NWU1ODNjMzAxYWQzYmFjYzc5MzI5NGRjOWMwOWJjOWQ5ZTVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-529770066\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-171085549 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-171085549\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:25:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhkK2szZFRtN3JBbFZzS2RZK2pPU2c9PSIsInZhbHVlIjoidHFWNnJnT1FwaWpFcVZkNnc5UmZJZ0JJc0VXK2FlUkljY1JkeGVZUDhaeW1uZXpEeDJKOXVrOXg4cFR5VWRBdC96Rm1WK3ZzbGxMV1RURTVub0pmWjVYMHRLVHRSMnh3ZHg1TFNNZkxGQkdOb2VPZ0o2djZNYUNmZzZqYWdmSFBWOWZYbGgvYk5lb3l0VzhNY1hrWmhWdGV1L0dBaTZxa1JTcjZscFlqNVhwdHBCbmdOYmQwMWRjU2NXczh2bVBEeU9qU0wxdTV5cFRCcjZ0V2FId2RvODREWk8xTld4TUJobExrL2pwemJYNGo1NlNwblJZOWRYVGU1TGxkMktkSkF3L2F3TVpHdEpPNUJGbjhlc1dFSC9GMVJUeldjTlJsOWg0RHBtSXpuakpvQzVFd0EwZXU3VlQyd1d4T25mdExQWmgwNkVaNDZXMStlSnBoWEozYUdveElsV1VqK2p0djlXN25sV0JJdUtxakxjMUw0MzM3cWdiMjNJc1BhVisxbnA4UkpRSnRoc1ozYXljaGV3QVJ0cjlIUmNYdWd2Z2xqM0RCU09uZ29VVmdkdTJBOGZNTGdNMHNYd3BLR0FTSFFlUmV3L01ybmtGQnNaNytCMkJoQ1hzckl6eUdJMHF6UWtoUU9zT1RqMGN0Y2oxSzJML1E4WkE0L1lteS85Q3UiLCJtYWMiOiJiN2RmMTUzODMwMGU1N2YzMjc4YmI3MjlmOWJjYjQ2MWNlZjdiYWJkNWRjN2RlNjE1NDk1OGQ4NGNiZTQzMjE5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:25:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlJWdFNPNENTeU8xdjlEZ29wQWxGd3c9PSIsInZhbHVlIjoiRVNsVU5YbGRIZ2crbVhPZWJFY00rbWVsZEtua1d3ZUlIWlkrVFhnT1FBbllVY2gwQVZOR3VkSm01SmU4YjErdEwzcFluNTJoRXVIM0l4TTlrckpFUHlXQ0h4a2NYNk5Zdi9WcVJzamY1UlBabEpVVTdXNkZLc3pPZUl4a0xQbTduQUhhMy81YU5hSHdwdGNKbndaTDN2UUs0TlJhMGgwZ2Z0SHdrY3ZxK1FQY3ZTZ1RjcWFQc0pFcFVjMHZtaExxb1Irc2ZtWmlCbDhXOWFrVGpTUmljZm5JdXVkWVd2KzlSWVhUZXJTRzVDUXBFWE9wV3pvSHMxYStjZWsxQ1BIbHk1M3plY3FGMGx5TmhobUk5b0d3S2syS0V4TnpKVkk5bzUwZmgwbGNXSkZEQTg5UU5LMXVhSldQbDQ2c0pNUENCT0dscmpLdTlPR1Bmc3JrTVhyb01IM2JYRE1QYnY4ckdRVVJFY2liZVB2VTZrcUpkblpRMHVFWDlpNVBWSXhuaFB0cmpBYkRwSU1YcmpqNkRoZVlUZm5PWWJ0em1CNWVqSDFENGNWSXUrVldvelFyVnVXREVLM2pvdURrOEN1SnBsUW1VQTdvT0lxZFNkVk90L0hRcmtKTjNpaUFGZ2J1bkxISTBsbU5lZDB5M1NDcjN4cEY0NTN6UGNVTjhZekoiLCJtYWMiOiI5MzNlNTE5NjUxNDQ5MjEwNjY1MGNkNjllNDA5YWFjMzhmYWY1MDE4Y2U4MDdlMTRhYTBlYzIyNDcxODU0MTJjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:25:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhkK2szZFRtN3JBbFZzS2RZK2pPU2c9PSIsInZhbHVlIjoidHFWNnJnT1FwaWpFcVZkNnc5UmZJZ0JJc0VXK2FlUkljY1JkeGVZUDhaeW1uZXpEeDJKOXVrOXg4cFR5VWRBdC96Rm1WK3ZzbGxMV1RURTVub0pmWjVYMHRLVHRSMnh3ZHg1TFNNZkxGQkdOb2VPZ0o2djZNYUNmZzZqYWdmSFBWOWZYbGgvYk5lb3l0VzhNY1hrWmhWdGV1L0dBaTZxa1JTcjZscFlqNVhwdHBCbmdOYmQwMWRjU2NXczh2bVBEeU9qU0wxdTV5cFRCcjZ0V2FId2RvODREWk8xTld4TUJobExrL2pwemJYNGo1NlNwblJZOWRYVGU1TGxkMktkSkF3L2F3TVpHdEpPNUJGbjhlc1dFSC9GMVJUeldjTlJsOWg0RHBtSXpuakpvQzVFd0EwZXU3VlQyd1d4T25mdExQWmgwNkVaNDZXMStlSnBoWEozYUdveElsV1VqK2p0djlXN25sV0JJdUtxakxjMUw0MzM3cWdiMjNJc1BhVisxbnA4UkpRSnRoc1ozYXljaGV3QVJ0cjlIUmNYdWd2Z2xqM0RCU09uZ29VVmdkdTJBOGZNTGdNMHNYd3BLR0FTSFFlUmV3L01ybmtGQnNaNytCMkJoQ1hzckl6eUdJMHF6UWtoUU9zT1RqMGN0Y2oxSzJML1E4WkE0L1lteS85Q3UiLCJtYWMiOiJiN2RmMTUzODMwMGU1N2YzMjc4YmI3MjlmOWJjYjQ2MWNlZjdiYWJkNWRjN2RlNjE1NDk1OGQ4NGNiZTQzMjE5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:25:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlJWdFNPNENTeU8xdjlEZ29wQWxGd3c9PSIsInZhbHVlIjoiRVNsVU5YbGRIZ2crbVhPZWJFY00rbWVsZEtua1d3ZUlIWlkrVFhnT1FBbllVY2gwQVZOR3VkSm01SmU4YjErdEwzcFluNTJoRXVIM0l4TTlrckpFUHlXQ0h4a2NYNk5Zdi9WcVJzamY1UlBabEpVVTdXNkZLc3pPZUl4a0xQbTduQUhhMy81YU5hSHdwdGNKbndaTDN2UUs0TlJhMGgwZ2Z0SHdrY3ZxK1FQY3ZTZ1RjcWFQc0pFcFVjMHZtaExxb1Irc2ZtWmlCbDhXOWFrVGpTUmljZm5JdXVkWVd2KzlSWVhUZXJTRzVDUXBFWE9wV3pvSHMxYStjZWsxQ1BIbHk1M3plY3FGMGx5TmhobUk5b0d3S2syS0V4TnpKVkk5bzUwZmgwbGNXSkZEQTg5UU5LMXVhSldQbDQ2c0pNUENCT0dscmpLdTlPR1Bmc3JrTVhyb01IM2JYRE1QYnY4ckdRVVJFY2liZVB2VTZrcUpkblpRMHVFWDlpNVBWSXhuaFB0cmpBYkRwSU1YcmpqNkRoZVlUZm5PWWJ0em1CNWVqSDFENGNWSXUrVldvelFyVnVXREVLM2pvdURrOEN1SnBsUW1VQTdvT0lxZFNkVk90L0hRcmtKTjNpaUFGZ2J1bkxISTBsbU5lZDB5M1NDcjN4cEY0NTN6UGNVTjhZekoiLCJtYWMiOiI5MzNlNTE5NjUxNDQ5MjEwNjY1MGNkNjllNDA5YWFjMzhmYWY1MDE4Y2U4MDdlMTRhYTBlYzIyNDcxODU0MTJjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:25:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>8</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tea-&#1588;&#1575;&#1607;&#1610;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>8</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}