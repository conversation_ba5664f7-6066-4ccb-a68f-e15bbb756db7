{"__meta": {"id": "Xef268f71c2ff85c92f62d70de94757ed", "datetime": "2025-06-17 15:32:01", "utime": **********.35824, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174319.839608, "end": **********.358286, "duration": 1.5186779499053955, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": 1750174319.839608, "relative_start": 0, "end": **********.142369, "relative_end": **********.142369, "duration": 1.3027610778808594, "duration_str": "1.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.142392, "relative_start": 1.3027839660644531, "end": **********.358291, "relative_end": 5.0067901611328125e-06, "duration": 0.21589899063110352, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46457448, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021970000000000003, "accumulated_duration_str": "21.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.266687, "duration": 0.01912, "duration_str": "19.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.028}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.320306, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.028, "width_percent": 8.102}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.333185, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 95.13, "width_percent": 4.87}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173975623%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlkT0N1OTRIbm13bUNYczhpa0xJcGc9PSIsInZhbHVlIjoiWnNCb3ZJdVdacVprbmJ3cDg3eVRyYWZIM3Y2ZVNYUzM0NjBUanJzTkJDc25jTXZUS3hsbnphYWFvVWxMdHRteHlTTVFLRS96cXVUYnVOMVpUeGNWMkNOQ0krNDZrdExtUWUzZ1F5ejdLb1E2citDMkMyYko0bnVNU1hPdjNRZkpQWkN6SHRRVUViSjZudzVhODE4U3hGMHIyaXpGdkxlWjVHbkdyOXJodS9tVTErNTI4UzlqVXlGLzhvVkNlN0NHTjNMRC9jQVNobHpkN012enVzQk5yQmNWeXJUUk5IcEdWLzJUb2ZPcXhqQmR0a3NidWtUOGtteXI5dEgxTDNYUHhZVGI3b1p1TURqc2JwZjA5Y0lhWS9uUEs5dTVsVUcxaVRUbHNJTzRjS29yZHNacE9OYkFSNWRqTWtaemNqSVcvSmwvdGtMdHBGVzdPQ3UyZE9TR0FyWFphakgwaXVqaWcyOXMyeEVrSXRiRTJMSTFuRHQ2bDBxdEhxQWhXWFE2VFdxRUw2eHRQSGNKeGJJbmlzbGlxalkwNVovT3k4RytxYWQzNys0dnNzQ040SFozUEFQNjE3dkRvR085ZWJ4eWtCSmtiNUJFQmZqcTJuUTJ0Y012VXk3MDI0MkozejBmVElIVjdDb1BNNWF6RkQ4TTFHU3V3MDV6QjUwc1RjRCsiLCJtYWMiOiJjMGZiNWU2NWYwY2QzYmNkNmNkMjcyOWNiODAzZDk1Yjk0OGZlNTdhYWFlM2IyN2FmZWFlNzgxMGEyZTgzNzQ3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InBDTXJTcGhTZUppWGlGdCswMHZvdFE9PSIsInZhbHVlIjoiRkQrVmV2MlZ5NlBITG9JdDJqVGxRM2ZpRnNIb2lUa2lJRWkzOUlJNTQxSVJsb0Jkd3UxTkMyb2IydUN1Yms5VjNRV3U1ZWlDR3dmaEdqa0U5QUJLM3pZdHhyV0Y3Z3lHd1ZHcGZpNFVVS2QvdGJnMWp3dURtUUNEQVF5dTZ6YUJqa3ZHRnFGV1JidHR3WTFkS0MyQ2xObEI0cHVoVWkrcUorNlVRZlg1ZUk1K3NiU2hMYXB0ZFUyeVpBZVp4Q0hPMktVZHhVT3ZRRENBdGhLTlBPdVo2ZE0yUnMrbWJkRXdrOU5JUUNqSGwvNi9yRWtZdFRaRG5rM2ZvUGtRL283NUU2d1RmK01vMkxzYmdvQjBDaUdERjgxQXgxNHcvQngwVzhIM3ViRVlORTVpMjVQL3N4Wm93eFFBbXVCcGdBL2pkb3RLbWE3cjlFRW1GREcyMUFyTkVZRlhpN0kvMzF2ejJ0WUJ1S2dMMjQ2Um1pVzBoT090Si9mWE1VYjVBV1YyemFSdWhjY0RrcWxtUDdLWFdJNW1xYkJKMEZKOTArbHpKRlJicWhCakJsQXB5czBOV2Rjc0tjVHlpbWdIUVBOa2RJZml2SVJiVHM1bWZVSEtpcFQxaTNPclNXa3hJWllKdVZQd2ZlbFBpb2pscmlTOENuaXpPazIyOGtsZnc4RDgiLCJtYWMiOiIwMmFhMjdiZDg0ZjE1NTkwNTA5M2E2ZDQ1ODhlYWE1ZTc2NmJlNzkxYzNiMjJlMWJmM2YxYzA5ZGQ2NWNlODBmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1874276395 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1874276395\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-752211016 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:32:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJMdFNHZmNiRUxmMEl1NWRLOWV2THc9PSIsInZhbHVlIjoiNVVXWjE3eDA3SlV4amNsRXBXWUJTcXdYVFhFSDFGZ2d0YmI4YTNZbEl3ancrdDIxdWN2U1ZpSFEzaGxYdHZEZ3ZHQzV6UUpxMFJCK3dySmJSOWp0OHFUOUtKTjd0OEpLb1VFSDlsUkNuVU8zbWZTeDZMS1RmbHAzZzBadlVJemo1NjcyRGs5U0dNMGdPR05zN21TSWVGMmVxNmtGdktlbDRLS2FSU3NFMlpXVEw0c2NVU0F0VkxqK214THUrWkd6d3hJYW1UT3pmRjU4cEp5QXlHbDlEMHhEcSt4Vm1ERXRsTmNaNTNzUjl5NEJyd3lIL3o5OGhaSUQ1T0M2VnpaU2ZiZVNFMnNhVGQ5Zy8rNzlrdWluTzNTUG1wQ2xxdlE1ekdVbUgrWUN4dm5HYXZsYWg4SXRCUW5xdHNpWDdBS2hzSmxpdnRyeUJQcHMvalBNREpHS2dlRkFHUGJkbmswbHoraGkzQUlCOVlJcURHNk1Md0lhM0liOGluMk12WEhISnZuWGJNVUFsaFpxWGpRSEdHczZQR1loaDcyY1FuQjNCaWNyQUlBY3hXU21naFgxeEE2emFmWTdSRDB0cktJZFhMNTdldisvNzY3VzMxT1doZ0Fld0NvRjFIWGdDQW1XNkhzaEgrZWFFaDFTWllYM2FwNHR3QTYxaS9uRUdkTDUiLCJtYWMiOiJhNjVjZjk3YTNkMGIzMDEyYjVjYWZkYTZiZWJjODUxNzU5YzIzZTU0ODAzOTY5ZWM1MmU4OGI5ZGU4NDEyZWUzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:32:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlZSRmVFem9veHZjQnJob2tabS90Z2c9PSIsInZhbHVlIjoielVWZ2VEVXprYUFZcnlTcEJnYkNVVGVPZGN3U3hEM1QzR2VId1NXZGxKOHFWcGNXN2tpTjhSNjdMUGZwVDJwV21QdXFvTlBYRnM2bmRDL3R5STNPQ1BFQWhlQTR4QjQrVE40WG1OMjhRUXFObllwYnFUdVNqQ3VBeDU1YWVSbjF2MEVtOVgyd2ZpUkYvdU5aMWs5bkdObiswaUtSdWgyUzN3NTNGejM1Ri9qQlh6K2ZXVExzWS9veXhWcHZQTWpQblVlMjhiYTg1TS9LQndIRW8vaFltcU54MUp1bVovWnFSQUtLSXp6Nm1mZzJVS2trVHl6cnJsczhKS3AxQkRSeXhxZGRoWlMyOXAzRzNJcjhvOEVkaFdlZFkrRjY0cS9DVHZlUVhBbXM1TndCeGRSVGttdXRwTk1YbXV5ZzRIWXhMNm5MVGhQV2owcUIyOTA4MUxVaXp1MnZ5dnZwcm5wS1dIZHIyZkhuNlBhOHVZUy9wL2hhekYzMGI2VVlxNmpxbjJGdEFoT1o1aEpUcDgrWHY0MDBLRmkrdW9uSmNvNTBteE9ZZHZ0UG01YU9GSmZtNjVkKy9qb09CRlpBRjA3RjZVVUpBSXQ5VmVJRWhpSkZ6dVlKRzNsc0VSSnpiQ1h6S3lZL3VPNmgxNWh6L3ExR3FOaXlIWVNKZXRRMElVV04iLCJtYWMiOiI3OGYyMDBkNmM3MzNiNTA4ZmE2YmRjMDI2OGJhYmQ2ZjM2N2U4MTUwMjI1ZDU3NDQ2MDRjN2JmNzBiMmYyYjk5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:32:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJMdFNHZmNiRUxmMEl1NWRLOWV2THc9PSIsInZhbHVlIjoiNVVXWjE3eDA3SlV4amNsRXBXWUJTcXdYVFhFSDFGZ2d0YmI4YTNZbEl3ancrdDIxdWN2U1ZpSFEzaGxYdHZEZ3ZHQzV6UUpxMFJCK3dySmJSOWp0OHFUOUtKTjd0OEpLb1VFSDlsUkNuVU8zbWZTeDZMS1RmbHAzZzBadlVJemo1NjcyRGs5U0dNMGdPR05zN21TSWVGMmVxNmtGdktlbDRLS2FSU3NFMlpXVEw0c2NVU0F0VkxqK214THUrWkd6d3hJYW1UT3pmRjU4cEp5QXlHbDlEMHhEcSt4Vm1ERXRsTmNaNTNzUjl5NEJyd3lIL3o5OGhaSUQ1T0M2VnpaU2ZiZVNFMnNhVGQ5Zy8rNzlrdWluTzNTUG1wQ2xxdlE1ekdVbUgrWUN4dm5HYXZsYWg4SXRCUW5xdHNpWDdBS2hzSmxpdnRyeUJQcHMvalBNREpHS2dlRkFHUGJkbmswbHoraGkzQUlCOVlJcURHNk1Md0lhM0liOGluMk12WEhISnZuWGJNVUFsaFpxWGpRSEdHczZQR1loaDcyY1FuQjNCaWNyQUlBY3hXU21naFgxeEE2emFmWTdSRDB0cktJZFhMNTdldisvNzY3VzMxT1doZ0Fld0NvRjFIWGdDQW1XNkhzaEgrZWFFaDFTWllYM2FwNHR3QTYxaS9uRUdkTDUiLCJtYWMiOiJhNjVjZjk3YTNkMGIzMDEyYjVjYWZkYTZiZWJjODUxNzU5YzIzZTU0ODAzOTY5ZWM1MmU4OGI5ZGU4NDEyZWUzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:32:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlZSRmVFem9veHZjQnJob2tabS90Z2c9PSIsInZhbHVlIjoielVWZ2VEVXprYUFZcnlTcEJnYkNVVGVPZGN3U3hEM1QzR2VId1NXZGxKOHFWcGNXN2tpTjhSNjdMUGZwVDJwV21QdXFvTlBYRnM2bmRDL3R5STNPQ1BFQWhlQTR4QjQrVE40WG1OMjhRUXFObllwYnFUdVNqQ3VBeDU1YWVSbjF2MEVtOVgyd2ZpUkYvdU5aMWs5bkdObiswaUtSdWgyUzN3NTNGejM1Ri9qQlh6K2ZXVExzWS9veXhWcHZQTWpQblVlMjhiYTg1TS9LQndIRW8vaFltcU54MUp1bVovWnFSQUtLSXp6Nm1mZzJVS2trVHl6cnJsczhKS3AxQkRSeXhxZGRoWlMyOXAzRzNJcjhvOEVkaFdlZFkrRjY0cS9DVHZlUVhBbXM1TndCeGRSVGttdXRwTk1YbXV5ZzRIWXhMNm5MVGhQV2owcUIyOTA4MUxVaXp1MnZ5dnZwcm5wS1dIZHIyZkhuNlBhOHVZUy9wL2hhekYzMGI2VVlxNmpxbjJGdEFoT1o1aEpUcDgrWHY0MDBLRmkrdW9uSmNvNTBteE9ZZHZ0UG01YU9GSmZtNjVkKy9qb09CRlpBRjA3RjZVVUpBSXQ5VmVJRWhpSkZ6dVlKRzNsc0VSSnpiQ1h6S3lZL3VPNmgxNWh6L3ExR3FOaXlIWVNKZXRRMElVV04iLCJtYWMiOiI3OGYyMDBkNmM3MzNiNTA4ZmE2YmRjMDI2OGJhYmQ2ZjM2N2U4MTUwMjI1ZDU3NDQ2MDRjN2JmNzBiMmYyYjk5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:32:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-752211016\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}