{"__meta": {"id": "Xf87beadc2fbc4d9730b2719c23d0fb2e", "datetime": "2025-06-17 15:32:07", "utime": **********.432403, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174326.063629, "end": **********.432436, "duration": 1.368807077407837, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1750174326.063629, "relative_start": 0, "end": **********.25596, "relative_end": **********.25596, "duration": 1.192331075668335, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.255989, "relative_start": 1.1923601627349854, "end": **********.432439, "relative_end": 3.0994415283203125e-06, "duration": 0.17645001411437988, "duration_str": "176ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46092928, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02003, "accumulated_duration_str": "20.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.34197, "duration": 0.01764, "duration_str": "17.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.068}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3900368, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.068, "width_percent": 5.492}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.409577, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.56, "width_percent": 6.44}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-752073445 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-752073445\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1507605334 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1507605334\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-465750993 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-465750993\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-497616561 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750174320617%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpSMmkwN1BsZEN1ZFJsVU5vMTE1cUE9PSIsInZhbHVlIjoiVFdKN1pCZGVFRmE5M0F1RmpWT0Y0UVdHY2pVdWtOMXBINjZCaEpjVFNMVnpiOEtXYkxTR2cwblBWNjQ3VDdwRTlWK3RldTlHYzFNeU0rN1cxVWNSc1Q0RjdyVlZkc3N6d1VINGJGdTkyWit1UFExU1NXUElqRW9PeDI1dmordXVxZTdERmZjNlY5K2JZQVRwcHpDZWZBV0hmRzNSc3J4cXY3dDZSVUFHZU5oZHZweUlQUElBZlMxcUpjVnZ0Z1pkTEZ0RHBqNmN6Q2taWXVQYVZqU2wzcnF0OUFIc0xIVkRIZVpqVXMrM1lBQkJsUHRBNW9uemtRdVpmMzBLV3U4TTd6UzNJVC9vektjUTFzcHNpS3dkZkV1SVpyZGlGek96aVJkUXJnV2xkQ0Rab0hPNUZNZFp3UXFLeC9qZlBabGViSjRlUjA3alY5dWRMWXVCY3dRR0xUclJhK3p6RFNSd3diNWtVNzBTekpTcU9LQ1FEcUd4ZTZYckp3cHZSTlQvQmREYy85ZGtma2VnS2JHSFBObEx0dTkvWmFHREJKQlhsSlVWd3IreWo4bW9oWGxJdmhPN3dNZ3UreW9TSk9nWW9sVHVUME5KbitIRHJBOVF6ZE84VWl5OVlIbEdEK0pQaFQ2S3BnNXpXMGJuYlQ0a052dGtSOFpMK0RNOG9BRjMiLCJtYWMiOiI2YzRiYjMyNGFjZjE1NWExZjU4NmI4YzVlNTk4M2Y3MmU5ODBiNTMzNWQxMGU1M2ZlNGRmM2QwN2FjMGQwNDNiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZjTVRMeDJqUm93SzNvUnRjOGNTRnc9PSIsInZhbHVlIjoiazRJNmJwMCs4cU4wV0luSVEycjVnWmdvaWlBRHk5T01yUGpHcEJBb0RkbzdSSjRKSVhITDNkUHk1SnJpMVMvQUNnUWJ3ZWZTSUVja2pYTS9qQU1LemxXM1NNYjJOQ2cxdGpybWlJYTNjcHY0NVVJR09nZlJXSTV6dWF2YmduRVdwK2ZndUw3SCtSakJhZXVaNUhDNk1NZmU4Q2MzaXNCY3JYdjI0T1gydlpJMnVXYk5zM2hVcjV3YXo5R3M2Z0Z3MjBTSU5JRGdWdzhpb1VPSDFlS0E2Qk1nZmJBV2Nvby9xVFVZU3k3RFZxSFRLTDgzMnZGYXBxTWlxTGhGWWlYVFJ1VlpYcjU4Q2Y5Y0ZOV3ZxZmNkd2Y0bjhiMkU5bGpoc0JtempDQVRZWE85ZTArRDIzU3ZvcUx0azB2bVNucGNrdTUxNXp1dEFITENpYTM1UDc5QStvR2tOem8zRk0yVi9DbnBLb2EyaUZOVjVNbTNlWDl6U2FpWWlQNllRVlJsMUhXNCs5RndtelNWcWxBRXVrcm1NS1dEM2h1ek02SDVVQ1ZKcjNzOUlzUUUrc1RWK3MweXZ6SjhTZ1lRUEdDdnd3L2tGWHZNYTRDNTR3OUFzT3NsR1hkNkRlTHN2T0dSalpnN2Y2Znl0T1lrNTYvcHFUN1UrbjVUMmtnTmF1UEIiLCJtYWMiOiIwMGFiODc3YjUxMzBlYjZjOWZlNjBmNjQyMGZkY2Q0MDdkYzAwMTkwY2ExOWI1YTE4YmZmOTcwNGNhY2FjMTIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-497616561\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1103819655 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1103819655\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-336435869 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:32:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRDeGlZb21zNEV5YU56dTcwUHF4Smc9PSIsInZhbHVlIjoiR1d6cG5LalRMM1B2NVd4K0hwRytLeXZYUExZL1JYamdmMjBhZFBwU3RDcmJ4U3ZZRzVmdk9SQlA5VGR1THJJdFRmSjJQQm9CSHQ3NjZxUGk1WTU2NUh1YWswM2c0b2xGQU92eXhQUlg1UlJ1eE5ROGp3MVBYSnkrNTVoaTdqT2FXNXFKaHIrSmRCSWJ6ZXlXeFBFdGgxazNtdWQyTGxyOHc2TEw5V2JKVFgxQzVvdWNDWldoaU02Ri9ZZGNjL3VDWnZ1ODdjTDhoV0JnM3JYazBIQ20vOHk2UlJCN2doMEpLS25uVHJreTNNOFpXblorWjJuekNSS2dGVlAxSytTL3ZGaXFsNW1NWXRUYUVncEtCWVlrOGI1UDNzb0l5c0Jjam02NUMwaDFvYndaRVFhbldHU3liWEdhNVdaWlp1dFV0OHNORXlRKzBxaFplcVE1VTNBK0E2QjMrYUQzaW5RTmF5M3VjY1dLMHZWV2o2a2tETGtrdlhCSU9ad1k0VHFxK3pObEhtTldYMC8vNEdWazBCeHBqYTZXWHZ1K2ZOb0dnSTlXK3FUdy8zeThhanBlL1g4b1pMY0xMNFRiL0NlUDFMZGlQYVQ1YzRLSkpPdjZRREdxT0tsRGpxK3hXOHduRndhdkpCVGtnU2dmMnlEeHFycUtnZ0dTL3ZaemVHa2giLCJtYWMiOiJiYjFhY2Q1MTAyYTBhYWU3N2UwOTIxNjQ4YjhiOTM4ZjM0ZWNjYzQ5NDA0ZWVhMDcwNTYxYjZjNjE2MWNjZDMwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:32:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii9aeUlsTm9PZ09HcXFzWFVKdG9KV0E9PSIsInZhbHVlIjoiaUhZMUc1NGI4VmxYZ05FVXF3NnRweWowZ0xvR1Q3K1p6bllHbFdCcUM5UHQzVFBnK2UwT0JlMkZyNXduVVU5bmdUMkF6Rm9sM0VjcE83NElhOHJuL0YwTzBKSERRN1pvY0dITjBCbnc1MFlNYXVjL2dSSUQ1akd5UGQ0YVkvL0NmRDlMVFFUYUFXWUVNRVVLdlVIYllnSFU5eXNXNis3M2xJaFE1bkthOVZpODAvUURZM25mdjlSekpZdjUvblk3TVhoRTBJSkFiN0RZS25VQW03M2NNdlpSTU9tQ2tyUWJvQmxQUExvTGUzMzRIMkxnVG55TS9vWUFYalZqQlcrdVh4VFY3WEdkOWNTWi9yWElUcU0yL1VOSVE1eFRNb2FVN1RSSmZDOUZLYU5NeHVYaVd5YWM4c1dUaVBKV29WemFPWVoxWE5zbSt3dkptb0g1cWZtdWMvM2s4OGYwTmtHZWVDSlhEdnpyVUI4bjI2amJkb09NYTYrdi9HMU9RaTNLbFNjam9Xbks5MHNVaVkyNmtKNmlMN3BhOTY3eHlXTURkZE5LR0t5b2hPTTlybFBWTDMzaUs3akE5T0ZQT01RbXd0enZuOEZVYndrcXU4alh1YlJMQWRsVFRHMjZxN01GZEZzYTEwaGRtR21JYVU0ZldjMzJhSEJxWnRxNE9iWVEiLCJtYWMiOiJjNWNmMTkwZTA0YjE0OTAwOTJhNTk4YjhjMWVlODI1ZjUwN2RkNTRiNGY0YWU3NTMzYzAxYmZkZmIxYWZiMWRmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:32:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRDeGlZb21zNEV5YU56dTcwUHF4Smc9PSIsInZhbHVlIjoiR1d6cG5LalRMM1B2NVd4K0hwRytLeXZYUExZL1JYamdmMjBhZFBwU3RDcmJ4U3ZZRzVmdk9SQlA5VGR1THJJdFRmSjJQQm9CSHQ3NjZxUGk1WTU2NUh1YWswM2c0b2xGQU92eXhQUlg1UlJ1eE5ROGp3MVBYSnkrNTVoaTdqT2FXNXFKaHIrSmRCSWJ6ZXlXeFBFdGgxazNtdWQyTGxyOHc2TEw5V2JKVFgxQzVvdWNDWldoaU02Ri9ZZGNjL3VDWnZ1ODdjTDhoV0JnM3JYazBIQ20vOHk2UlJCN2doMEpLS25uVHJreTNNOFpXblorWjJuekNSS2dGVlAxSytTL3ZGaXFsNW1NWXRUYUVncEtCWVlrOGI1UDNzb0l5c0Jjam02NUMwaDFvYndaRVFhbldHU3liWEdhNVdaWlp1dFV0OHNORXlRKzBxaFplcVE1VTNBK0E2QjMrYUQzaW5RTmF5M3VjY1dLMHZWV2o2a2tETGtrdlhCSU9ad1k0VHFxK3pObEhtTldYMC8vNEdWazBCeHBqYTZXWHZ1K2ZOb0dnSTlXK3FUdy8zeThhanBlL1g4b1pMY0xMNFRiL0NlUDFMZGlQYVQ1YzRLSkpPdjZRREdxT0tsRGpxK3hXOHduRndhdkpCVGtnU2dmMnlEeHFycUtnZ0dTL3ZaemVHa2giLCJtYWMiOiJiYjFhY2Q1MTAyYTBhYWU3N2UwOTIxNjQ4YjhiOTM4ZjM0ZWNjYzQ5NDA0ZWVhMDcwNTYxYjZjNjE2MWNjZDMwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:32:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii9aeUlsTm9PZ09HcXFzWFVKdG9KV0E9PSIsInZhbHVlIjoiaUhZMUc1NGI4VmxYZ05FVXF3NnRweWowZ0xvR1Q3K1p6bllHbFdCcUM5UHQzVFBnK2UwT0JlMkZyNXduVVU5bmdUMkF6Rm9sM0VjcE83NElhOHJuL0YwTzBKSERRN1pvY0dITjBCbnc1MFlNYXVjL2dSSUQ1akd5UGQ0YVkvL0NmRDlMVFFUYUFXWUVNRVVLdlVIYllnSFU5eXNXNis3M2xJaFE1bkthOVZpODAvUURZM25mdjlSekpZdjUvblk3TVhoRTBJSkFiN0RZS25VQW03M2NNdlpSTU9tQ2tyUWJvQmxQUExvTGUzMzRIMkxnVG55TS9vWUFYalZqQlcrdVh4VFY3WEdkOWNTWi9yWElUcU0yL1VOSVE1eFRNb2FVN1RSSmZDOUZLYU5NeHVYaVd5YWM4c1dUaVBKV29WemFPWVoxWE5zbSt3dkptb0g1cWZtdWMvM2s4OGYwTmtHZWVDSlhEdnpyVUI4bjI2amJkb09NYTYrdi9HMU9RaTNLbFNjam9Xbks5MHNVaVkyNmtKNmlMN3BhOTY3eHlXTURkZE5LR0t5b2hPTTlybFBWTDMzaUs3akE5T0ZQT01RbXd0enZuOEZVYndrcXU4alh1YlJMQWRsVFRHMjZxN01GZEZzYTEwaGRtR21JYVU0ZldjMzJhSEJxWnRxNE9iWVEiLCJtYWMiOiJjNWNmMTkwZTA0YjE0OTAwOTJhNTk4YjhjMWVlODI1ZjUwN2RkNTRiNGY0YWU3NTMzYzAxYmZkZmIxYWZiMWRmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:32:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-336435869\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-944194908 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-944194908\", {\"maxDepth\":0})</script>\n"}}