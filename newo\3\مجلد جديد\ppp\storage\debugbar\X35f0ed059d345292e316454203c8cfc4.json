{"__meta": {"id": "X35f0ed059d345292e316454203c8cfc4", "datetime": "2025-06-17 15:32:07", "utime": **********.431561, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174326.063629, "end": **********.431593, "duration": 1.3679640293121338, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1750174326.063629, "relative_start": 0, "end": **********.25338, "relative_end": **********.25338, "duration": 1.189751148223877, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.253398, "relative_start": 1.1897690296173096, "end": **********.431596, "relative_end": 3.0994415283203125e-06, "duration": 0.17819809913635254, "duration_str": "178ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46105856, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019059999999999997, "accumulated_duration_str": "19.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.343497, "duration": 0.01611, "duration_str": "16.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.523}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.388395, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.523, "width_percent": 7.345}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.407649, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.868, "width_percent": 8.132}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-993485903 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-993485903\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1803032517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1803032517\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1480413399 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1480413399\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1078237969 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750174320617%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpSMmkwN1BsZEN1ZFJsVU5vMTE1cUE9PSIsInZhbHVlIjoiVFdKN1pCZGVFRmE5M0F1RmpWT0Y0UVdHY2pVdWtOMXBINjZCaEpjVFNMVnpiOEtXYkxTR2cwblBWNjQ3VDdwRTlWK3RldTlHYzFNeU0rN1cxVWNSc1Q0RjdyVlZkc3N6d1VINGJGdTkyWit1UFExU1NXUElqRW9PeDI1dmordXVxZTdERmZjNlY5K2JZQVRwcHpDZWZBV0hmRzNSc3J4cXY3dDZSVUFHZU5oZHZweUlQUElBZlMxcUpjVnZ0Z1pkTEZ0RHBqNmN6Q2taWXVQYVZqU2wzcnF0OUFIc0xIVkRIZVpqVXMrM1lBQkJsUHRBNW9uemtRdVpmMzBLV3U4TTd6UzNJVC9vektjUTFzcHNpS3dkZkV1SVpyZGlGek96aVJkUXJnV2xkQ0Rab0hPNUZNZFp3UXFLeC9qZlBabGViSjRlUjA3alY5dWRMWXVCY3dRR0xUclJhK3p6RFNSd3diNWtVNzBTekpTcU9LQ1FEcUd4ZTZYckp3cHZSTlQvQmREYy85ZGtma2VnS2JHSFBObEx0dTkvWmFHREJKQlhsSlVWd3IreWo4bW9oWGxJdmhPN3dNZ3UreW9TSk9nWW9sVHVUME5KbitIRHJBOVF6ZE84VWl5OVlIbEdEK0pQaFQ2S3BnNXpXMGJuYlQ0a052dGtSOFpMK0RNOG9BRjMiLCJtYWMiOiI2YzRiYjMyNGFjZjE1NWExZjU4NmI4YzVlNTk4M2Y3MmU5ODBiNTMzNWQxMGU1M2ZlNGRmM2QwN2FjMGQwNDNiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZjTVRMeDJqUm93SzNvUnRjOGNTRnc9PSIsInZhbHVlIjoiazRJNmJwMCs4cU4wV0luSVEycjVnWmdvaWlBRHk5T01yUGpHcEJBb0RkbzdSSjRKSVhITDNkUHk1SnJpMVMvQUNnUWJ3ZWZTSUVja2pYTS9qQU1LemxXM1NNYjJOQ2cxdGpybWlJYTNjcHY0NVVJR09nZlJXSTV6dWF2YmduRVdwK2ZndUw3SCtSakJhZXVaNUhDNk1NZmU4Q2MzaXNCY3JYdjI0T1gydlpJMnVXYk5zM2hVcjV3YXo5R3M2Z0Z3MjBTSU5JRGdWdzhpb1VPSDFlS0E2Qk1nZmJBV2Nvby9xVFVZU3k3RFZxSFRLTDgzMnZGYXBxTWlxTGhGWWlYVFJ1VlpYcjU4Q2Y5Y0ZOV3ZxZmNkd2Y0bjhiMkU5bGpoc0JtempDQVRZWE85ZTArRDIzU3ZvcUx0azB2bVNucGNrdTUxNXp1dEFITENpYTM1UDc5QStvR2tOem8zRk0yVi9DbnBLb2EyaUZOVjVNbTNlWDl6U2FpWWlQNllRVlJsMUhXNCs5RndtelNWcWxBRXVrcm1NS1dEM2h1ek02SDVVQ1ZKcjNzOUlzUUUrc1RWK3MweXZ6SjhTZ1lRUEdDdnd3L2tGWHZNYTRDNTR3OUFzT3NsR1hkNkRlTHN2T0dSalpnN2Y2Znl0T1lrNTYvcHFUN1UrbjVUMmtnTmF1UEIiLCJtYWMiOiIwMGFiODc3YjUxMzBlYjZjOWZlNjBmNjQyMGZkY2Q0MDdkYzAwMTkwY2ExOWI1YTE4YmZmOTcwNGNhY2FjMTIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078237969\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1235384795 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1235384795\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1509649594 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:32:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJFVUp6L01kZzlNR2I5dldTWW9rTFE9PSIsInZhbHVlIjoidU1qSjRuckJ4c2NXdWRHb3hXVWNoak90VklXWEJOY1hCejZtbjRYTGpuWHVMTk53ckdqcHNpVFU0RXhiVXZuSFVRTnRJU20wK0RXMXhQZDM0VUpGbGNZODRDeTVPUzZNL2ZZajl4dWp2M0RSa2t1bEY1WHNvL1U1MjhROC9HMlBsbGNJZnNZRFQyY0hUa1FOYzhYdXlPYXpBenpTN0Fkbjg3L1Z4dmhFTGRUQjJjb1R2Mmw0TnpLWkNQeDdBTGZRVVpuR1VEa01aSU1BeXg2emdlZ20vYnBwb1FKVGYrUG1KMGg0elVnQnc1clM1VjZoeUlPcDNjQTMwOUFFZFlac3crNHhyWmx4T2E4Um0wTFQxQ21vY1JUbGtmdld3aHJkM1NPNnpWbmY2UEV5eVIwM1JKTkNkczZnRzF5TkdLTEUrOVR4Y1JxZ21VaHZURklyelg0aXNsdllmL0dnNFRJRm94TU0wQ3lsZk9LUFp0SUtJbUZHV2tleERRYW1QSWM4T3laZE5ha1BFUXlZYk8yOTRBazhXUmxsR1YzV0o1cjZDaHZLSjJvWUFSVGk1Nm8xbDBLRVkyeFJISlZUZGg0cFZRV1puSHpKeVg3QUxiZWFScmlFUHNvQXVBRlVRTzUraER2MzloWkR1K2RqazE4N0NVb0xyRmhjMHNDS0ZNSW8iLCJtYWMiOiIzOTAwNTdjYTdjNDNjZGZlNjY5YzUxNWYyMjdhMTZmZWZhMmY2YmRjMjJkZGJmMDE1MGZhYzZkMGNmZTZkNTI1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:32:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlVKakpJNmtmSHFhTXNzOXBwRmVCQ3c9PSIsInZhbHVlIjoieTFLMThPWDBKMjJ5UEhzM2JGc21aRWZRU3RSb0F6MXZuRmtScENqV0owS2swQzIveDJPb0ptRTBkZEsxQndoOVJPTWdRR1NNL0F3V2tsbEdtNEtGUm1uOEZiV1MxZXNMZXk4QWp5dVR0WWk0eXVpL3JVc3d6L1ZYT0ZVZVFMckdKemhYM3N2c0o4VVBHeHJVbmw2MFRTRkkrVUNGblk2WnRpb0NieTNrN0IxbGkzaGRHaUgxYmkvR1RYV1lPR1N1ZTU4cTJON2N4QVlDWjFLNUdqTEhhclJFZzZiVGoxOEU0YVQ5Q25BWi92M0hMWEgwUUN0VHZHbVNFaHhSR1dPUUJYLytwOEdRTkdyNHEzeVUzS2oxc2hwVEN5V1h1T3NWeTBqRjNIaFdvN2psTFdkc0xYRUNZZDdydkZCNUlrenJyRlJZK0l5ZlFXRmVTZ1ZOdFk5S2VUTWkveXJ2OTg3aGNObmtVdlhoTW5sS2hOQ3RBcDJCZU9ubHpxWnYzaVZ0c1ovSWhEMlc0a1FNYTVSRG05b2M3R0E0aXZWUzdmRGdJMWpuTTZ4emNXR1BXQjBVS2FMYXdCWUQvbi84KzFxb2dVMjBIMXVPeEZBQnc0aUdzNUdobzhhZjlCUjcyZE9WRDVPanBCMncyQUFxMHpmVjJPQVk4TWRTOWkwelBCRS8iLCJtYWMiOiJjOTQ3NjJiNTIxNDQ2Zjg3ZDFiZjhjZmE1MTJhYWRiMDAxZTdkNTM0OWFmNjA4NWQxMjBlZTI5MTE0NzdkNGNlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:32:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJFVUp6L01kZzlNR2I5dldTWW9rTFE9PSIsInZhbHVlIjoidU1qSjRuckJ4c2NXdWRHb3hXVWNoak90VklXWEJOY1hCejZtbjRYTGpuWHVMTk53ckdqcHNpVFU0RXhiVXZuSFVRTnRJU20wK0RXMXhQZDM0VUpGbGNZODRDeTVPUzZNL2ZZajl4dWp2M0RSa2t1bEY1WHNvL1U1MjhROC9HMlBsbGNJZnNZRFQyY0hUa1FOYzhYdXlPYXpBenpTN0Fkbjg3L1Z4dmhFTGRUQjJjb1R2Mmw0TnpLWkNQeDdBTGZRVVpuR1VEa01aSU1BeXg2emdlZ20vYnBwb1FKVGYrUG1KMGg0elVnQnc1clM1VjZoeUlPcDNjQTMwOUFFZFlac3crNHhyWmx4T2E4Um0wTFQxQ21vY1JUbGtmdld3aHJkM1NPNnpWbmY2UEV5eVIwM1JKTkNkczZnRzF5TkdLTEUrOVR4Y1JxZ21VaHZURklyelg0aXNsdllmL0dnNFRJRm94TU0wQ3lsZk9LUFp0SUtJbUZHV2tleERRYW1QSWM4T3laZE5ha1BFUXlZYk8yOTRBazhXUmxsR1YzV0o1cjZDaHZLSjJvWUFSVGk1Nm8xbDBLRVkyeFJISlZUZGg0cFZRV1puSHpKeVg3QUxiZWFScmlFUHNvQXVBRlVRTzUraER2MzloWkR1K2RqazE4N0NVb0xyRmhjMHNDS0ZNSW8iLCJtYWMiOiIzOTAwNTdjYTdjNDNjZGZlNjY5YzUxNWYyMjdhMTZmZWZhMmY2YmRjMjJkZGJmMDE1MGZhYzZkMGNmZTZkNTI1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:32:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlVKakpJNmtmSHFhTXNzOXBwRmVCQ3c9PSIsInZhbHVlIjoieTFLMThPWDBKMjJ5UEhzM2JGc21aRWZRU3RSb0F6MXZuRmtScENqV0owS2swQzIveDJPb0ptRTBkZEsxQndoOVJPTWdRR1NNL0F3V2tsbEdtNEtGUm1uOEZiV1MxZXNMZXk4QWp5dVR0WWk0eXVpL3JVc3d6L1ZYT0ZVZVFMckdKemhYM3N2c0o4VVBHeHJVbmw2MFRTRkkrVUNGblk2WnRpb0NieTNrN0IxbGkzaGRHaUgxYmkvR1RYV1lPR1N1ZTU4cTJON2N4QVlDWjFLNUdqTEhhclJFZzZiVGoxOEU0YVQ5Q25BWi92M0hMWEgwUUN0VHZHbVNFaHhSR1dPUUJYLytwOEdRTkdyNHEzeVUzS2oxc2hwVEN5V1h1T3NWeTBqRjNIaFdvN2psTFdkc0xYRUNZZDdydkZCNUlrenJyRlJZK0l5ZlFXRmVTZ1ZOdFk5S2VUTWkveXJ2OTg3aGNObmtVdlhoTW5sS2hOQ3RBcDJCZU9ubHpxWnYzaVZ0c1ovSWhEMlc0a1FNYTVSRG05b2M3R0E0aXZWUzdmRGdJMWpuTTZ4emNXR1BXQjBVS2FMYXdCWUQvbi84KzFxb2dVMjBIMXVPeEZBQnc0aUdzNUdobzhhZjlCUjcyZE9WRDVPanBCMncyQUFxMHpmVjJPQVk4TWRTOWkwelBCRS8iLCJtYWMiOiJjOTQ3NjJiNTIxNDQ2Zjg3ZDFiZjhjZmE1MTJhYWRiMDAxZTdkNTM0OWFmNjA4NWQxMjBlZTI5MTE0NzdkNGNlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:32:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509649594\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1132797816 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1132797816\", {\"maxDepth\":0})</script>\n"}}