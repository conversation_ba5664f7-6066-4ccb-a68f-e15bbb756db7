{"__meta": {"id": "X3d0c2af17f507ad7945a8a5399d3d652", "datetime": "2025-06-17 15:25:59", "utime": **********.069686, "method": "GET", "uri": "/pos-payment-type?vc_name=6&user_id=&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173957.603022, "end": **********.069719, "duration": 1.4666969776153564, "duration_str": "1.47s", "measures": [{"label": "Booting", "start": 1750173957.603022, "relative_start": 0, "end": **********.747429, "relative_end": **********.747429, "duration": 1.144406795501709, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.747453, "relative_start": 1.1444308757781982, "end": **********.069722, "relative_end": 2.86102294921875e-06, "duration": 0.3222689628601074, "duration_str": "322ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54081520, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.bill_type", "param_count": null, "params": [], "start": **********.058862, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\resources\\views/pos/bill_type.blade.phppos.bill_type", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fresources%2Fviews%2Fpos%2Fbill_type.blade.php&line=1", "ajax": false, "filename": "bill_type.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.bill_type"}]}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1505\" onclick=\"\">app/Http/Controllers/PosController.php:1505-1613</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.02184, "accumulated_duration_str": "21.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.858129, "duration": 0.01349, "duration_str": "13.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 61.767}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.896703, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 61.767, "width_percent": 5.266}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.94597, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 67.033, "width_percent": 7.189}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.95369, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 74.222, "width_percent": 5.449}, {"sql": "select * from `customers` where `name` = '6' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["6", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\PosController.php", "line": 1516}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.971241, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1516", "source": "app/Http/Controllers/PosController.php:1516", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1516", "ajax": false, "filename": "PosController.php", "line": "1516"}, "connection": "ty", "start_percent": 79.67, "width_percent": 6.41}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\PosController.php", "line": 1517}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.979662, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1517", "source": "app/Http/Controllers/PosController.php:1517", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1517", "ajax": false, "filename": "PosController.php", "line": "1517"}, "connection": "ty", "start_percent": 86.081, "width_percent": 4.258}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\PosController.php", "line": 612}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\PosController.php", "line": 1521}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.99034, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "PosController.php:612", "source": "app/Http/Controllers/PosController.php:612", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FPosController.php&line=612", "ajax": false, "filename": "PosController.php", "line": "612"}, "connection": "ty", "start_percent": 90.339, "width_percent": 4.762}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\PosController.php", "line": 1600}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.034899, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1600", "source": "app/Http/Controllers/PosController.php:1600", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1600", "ajax": false, "filename": "PosController.php", "line": "1600"}, "connection": "ty", "start_percent": 95.101, "width_percent": 4.899}]}, "models": {"data": {"App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-563032841 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-563032841\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.96754, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-581593454 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-581593454\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.988689, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  8 => array:9 [\n    \"name\" => \"tea-شاهي\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"8\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-21391778 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-21391778\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-967802178 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173924302%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlQUXhLWURwQjZxNE1lTzVCQU9EOXc9PSIsInZhbHVlIjoic2EvU1M3QWJseHZiaVhpM2FCd1JVOGJjS1l5OWFLanZaUERNUVVvcElMZG9xU3dmRFpsazlOdHVFWW9ja1hDRmR3MTFZam5tL3A0VHRkbTlKUjQ2ckp6M2lPMXZzQ1JMd0t1SkhSVjdnRm1GbFpNT1JlRk8wZk9NbjQ1VDNlMW1kck9EMWFYZ3V4RUtTTk1nUkQwdTkwa3kxQXpZYzhFVExkM25QTzVLV2piY0R4ckxWY3QvcW91NGFFZlZRSDl6aTlFWlFyR3F5NlZ5a2s3dENkbmp6UEszbWxpd0h4UVlpVy9aK21Ealpici9FbERERkZDdDh3cFp2QVlNQnZXRGNIeDNlZGNzdlVybHlwS01RbWxNK0FoRlpCRFdIZ1JkTXNPSFptTGZXd0RoU2h5TzkrTnhkTVo5Vjc3dUY4OEtRbTJqeUtwcVFVK2FxSldPUXJkT3ZKZERMV0JVM1BsUHRlbXE4bjdjSzA0YlB5TXdMVExHc3dCRXpMNzZHQ2F4cjdsbHk2WVRGYlBhV3RZZk9LR1M4Tjk1dDh4YUVMQWlBVHlrMFRPRlEwRFJlUzBvSnlHRHJaQnVNRFBPVjRVdzVXcVFvVFZiM2t3Vmt3Y0wrclo5VklyV1BzVzJUcmRFako2eVNYUThaUTlncFd3VGE4UDhqWUZKcnFNY25KZS8iLCJtYWMiOiI5YTNhNDE0ZDhmMjRiMDgxYzdhMzc0NjMzM2RkNTJlZTk5M2NlMDEzNjA4Y2QxYjllZDNlOTVkYzA4MTk4YmIxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkNOcTRmT3dyVWJHQmFYMjgydG45QUE9PSIsInZhbHVlIjoiaUZUTzVLZ1JHOFZjMGxrZzFzSXFUZnZoeFJVM3hCTitSaS9rQmdvcWpiWUsyaml2a3hmd05XTWNOa1hmN2x5aVVtZXhibUdLZjZMSTRvTFh6MVV4OWlhRFJoeUxXSTRNZzhMRUhHeitRRFNQWThIeWtUUHNMWUl6TWZzMTRidkJKZHBFU01sRCtocW9Ec2JIUGdGeWhOS3NMRGJ5TTFUaFEwMFNyTlVDUFRQQlRRb3I5cXN2dVJsOHZwbjlQWU1Pd0RuK3V2NmZvU2tzVVd5c3FIQW5rSWlNZ3hKeXdlajZWMFJkejBSeDNnN1BvOW5kczhzd3U5NXpNNXFRZlhwQ2VCMUNXWGpXeXpVMkZDVzBCQWlZbVZ0aDJhNXFoVWN4RC9QOUpaMDl1THlTMzlkRWcwTWRaeE1jWFJuMDR1S1VrWjNyb0JCNDVXcjRpMGFsSkUrNktkZk5RTmR0OTlRNE9pbFM2WkxTTHFuSXJzdE5TamxOdk5NSll0NzVvNnFrb2dWT1Y3QnJZQjdaWk53eHE5MFJkdXUwa0NZUVhUK0ZybVlXeGozNW9VbmZiaUVmTUh6ay9kRG5PMXFSQlZiR3NKcENjWHA3ZHd4TkRLTWZXdVZWVmFuVFV2eXhBVzZRN1JUM0tTem1EUEJFbExXOEx5aW14ZFErSTZVZlArbEYiLCJtYWMiOiJkYzM2Y2RiYTYxNDAyYjI3ZGI2ZGNmOTNiNDZkMGNlODNjYjQ0N2FhZjA4OTM0ZWZjYjA1ZGRlODZlNTk5NmUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-967802178\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-920911228 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920911228\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1070160559 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:25:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImthSFAxUjl0YUpnQVpNZ1QzTHhrVkE9PSIsInZhbHVlIjoiVUJPUFZSV1p1SXlwWkgyaWxwOTlSaGs2WWZiK1pzeGRrNDV1NlEwZHpoMFlpQWlQOWkwZzZkamU5MHljYk5ObGF5UjhQOGhZSVB3Mk1rUFZEc1RjN0paa291RUlyOHc3elBheUVHcnJjaEJOaEhzTGwzUE53OTQ0Ui9tb3VDV3FJazJwb3RPUGtEZGhGUU1vV0UzVkR0S3hHeWJ2MTZrNUtSMlhDUXNSY3NDdGZPSm50Mzd0K0dMSzB3WWlPaktocGduRlNiVDR2TE1vTVExZDVEQWhrYXltNGxwRHNqRUhiRmdmN0gxWjdrN1dxdVlUV0hodmZvVWhzZ0w2MW9pejIxVWZoVHIyWTBEY0JwbzNWdlJvRm9IdXJJeHN2cDVCbXc5aDRSeUJRcEh1MVc4MW5JWlcxemVnNXRYbnpjTkRMU0pVc2JjNzVIcEVlc0prZi8rd1dJSnQzdnZ2dmlGaE9HL1JMb010Mms2OCtid2tPK1NSaE1xck9nVGtQeW1FUGRTOGl2Q3FST0hhMnNQeENFd3F2N0VKdnlpV0E3UXhXYTdHMEVhWTRmUU5jNDdOY1BIZ09IQzJnOG1BU2tBN292TjF6Y0t5NHBhY08vY214YnRKM01QQjYzbjZZL1dIR1FKQVhQYUpQT1JvRXp6dmsxRTJldDFMK2grOGQ2R2kiLCJtYWMiOiJiYTZkZjkzMjIwODJlZTRmOTk4ZDQzMmFkNGVkOWI5ZTIwODc5ZjY1MTNkMmZjNGZjMWE3YzViY2Y5Mzg2ZTE1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:25:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IitLc0JCSiszWDJwN3Q2RGpQcXlQMlE9PSIsInZhbHVlIjoiYU1KcXVQazY3VUxYRHZLNFNEWUtNZTRnZzBETHQrNXl2KzVnbkVpUFVtaURlN2FzOUZIZ3dGaXhYTEc4ZWF1a3JaNWNpdW1OYmZCQ3MxWmsrN21yeS9ZbXhkWm42d3V3WTF1SkY5Q3VJTFZUSTdYMkwvRWtjbmw1aCtvZURFU1pmdythYzA5VE1lL2VzU0VFc0NQY2FwTUJPNEtzVGdXeHlvMGo4RDdEOTZERHN4VlIzaXhjMllSaEU0WVZvMy95VnJNdjkvK21WbUEyRGVIRjRyVDRFbnlSYUl2Qi93Mk1ZSnBwSkdvTDlUYnJPblVqVGlORGVFMEdFRzNHWXJoODJvSG9kS3hXRnpCeDM5bTZjcG9iMVZJOGk2Y0huRmVqY2pDbzRvVHcwRkZlL2R4TUVTR2hqZytmT3BBWXBtNnNDd09IbnVUdEJKS2R3THdMRFhjclJtMktVRUMrTGxPdjlOWnhQZWV2VUNOcFRJYXpJRlB4VXhwaWxyQWZQRitKeGNRZFZEd0tjQ3VnVWN4RFhZRFFHVGdjbGlMQmZIekYvYTQ1alBoNlRPODVMQkw2MFpjZS9wUHlsR1VhNjhtdldpa3ZWcXZBdnlqUDRhMW5hbG84RmViVU5XUU5CdXM3dy9RWG00UlhRTFlRTGZGbHBCRlFBdS9SNm1DYmpic1ciLCJtYWMiOiJhYWU3MWM2ZWIyYzQ2ZjBkMGJmYTc0ZWNmYTczMDg1NTY1YzUxNGY4MzYyNThjZTY1ZDZkOTVkNmUxNmNlMzVhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:25:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImthSFAxUjl0YUpnQVpNZ1QzTHhrVkE9PSIsInZhbHVlIjoiVUJPUFZSV1p1SXlwWkgyaWxwOTlSaGs2WWZiK1pzeGRrNDV1NlEwZHpoMFlpQWlQOWkwZzZkamU5MHljYk5ObGF5UjhQOGhZSVB3Mk1rUFZEc1RjN0paa291RUlyOHc3elBheUVHcnJjaEJOaEhzTGwzUE53OTQ0Ui9tb3VDV3FJazJwb3RPUGtEZGhGUU1vV0UzVkR0S3hHeWJ2MTZrNUtSMlhDUXNSY3NDdGZPSm50Mzd0K0dMSzB3WWlPaktocGduRlNiVDR2TE1vTVExZDVEQWhrYXltNGxwRHNqRUhiRmdmN0gxWjdrN1dxdVlUV0hodmZvVWhzZ0w2MW9pejIxVWZoVHIyWTBEY0JwbzNWdlJvRm9IdXJJeHN2cDVCbXc5aDRSeUJRcEh1MVc4MW5JWlcxemVnNXRYbnpjTkRMU0pVc2JjNzVIcEVlc0prZi8rd1dJSnQzdnZ2dmlGaE9HL1JMb010Mms2OCtid2tPK1NSaE1xck9nVGtQeW1FUGRTOGl2Q3FST0hhMnNQeENFd3F2N0VKdnlpV0E3UXhXYTdHMEVhWTRmUU5jNDdOY1BIZ09IQzJnOG1BU2tBN292TjF6Y0t5NHBhY08vY214YnRKM01QQjYzbjZZL1dIR1FKQVhQYUpQT1JvRXp6dmsxRTJldDFMK2grOGQ2R2kiLCJtYWMiOiJiYTZkZjkzMjIwODJlZTRmOTk4ZDQzMmFkNGVkOWI5ZTIwODc5ZjY1MTNkMmZjNGZjMWE3YzViY2Y5Mzg2ZTE1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:25:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IitLc0JCSiszWDJwN3Q2RGpQcXlQMlE9PSIsInZhbHVlIjoiYU1KcXVQazY3VUxYRHZLNFNEWUtNZTRnZzBETHQrNXl2KzVnbkVpUFVtaURlN2FzOUZIZ3dGaXhYTEc4ZWF1a3JaNWNpdW1OYmZCQ3MxWmsrN21yeS9ZbXhkWm42d3V3WTF1SkY5Q3VJTFZUSTdYMkwvRWtjbmw1aCtvZURFU1pmdythYzA5VE1lL2VzU0VFc0NQY2FwTUJPNEtzVGdXeHlvMGo4RDdEOTZERHN4VlIzaXhjMllSaEU0WVZvMy95VnJNdjkvK21WbUEyRGVIRjRyVDRFbnlSYUl2Qi93Mk1ZSnBwSkdvTDlUYnJPblVqVGlORGVFMEdFRzNHWXJoODJvSG9kS3hXRnpCeDM5bTZjcG9iMVZJOGk2Y0huRmVqY2pDbzRvVHcwRkZlL2R4TUVTR2hqZytmT3BBWXBtNnNDd09IbnVUdEJKS2R3THdMRFhjclJtMktVRUMrTGxPdjlOWnhQZWV2VUNOcFRJYXpJRlB4VXhwaWxyQWZQRitKeGNRZFZEd0tjQ3VnVWN4RFhZRFFHVGdjbGlMQmZIekYvYTQ1alBoNlRPODVMQkw2MFpjZS9wUHlsR1VhNjhtdldpa3ZWcXZBdnlqUDRhMW5hbG84RmViVU5XUU5CdXM3dy9RWG00UlhRTFlRTGZGbHBCRlFBdS9SNm1DYmpic1ciLCJtYWMiOiJhYWU3MWM2ZWIyYzQ2ZjBkMGJmYTc0ZWNmYTczMDg1NTY1YzUxNGY4MzYyNThjZTY1ZDZkOTVkNmUxNmNlMzVhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:25:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1070160559\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>8</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tea-&#1588;&#1575;&#1607;&#1610;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>8</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}