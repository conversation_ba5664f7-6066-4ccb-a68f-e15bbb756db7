{"__meta": {"id": "X3e43e87c517c74428f53bf8afe4f854a", "datetime": "2025-06-17 15:16:13", "utime": **********.391029, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173371.924894, "end": **********.391064, "duration": 1.466169834136963, "duration_str": "1.47s", "measures": [{"label": "Booting", "start": 1750173371.924894, "relative_start": 0, "end": **********.21724, "relative_end": **********.21724, "duration": 1.2923460006713867, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.217265, "relative_start": 1.2923707962036133, "end": **********.391068, "relative_end": 4.0531158447265625e-06, "duration": 0.17380309104919434, "duration_str": "174ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46106344, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01811, "accumulated_duration_str": "18.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2958338, "duration": 0.01551, "duration_str": "15.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.643}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3439982, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.643, "width_percent": 6.461}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.366683, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.104, "width_percent": 7.896}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-385395051 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-385395051\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-346337869 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-346337869\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-213491216 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-213491216\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1077894971 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173364129%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1hN21mU0p6WW4wcTlQWEJmVlJxbFE9PSIsInZhbHVlIjoiMjN2eXVrbjdPM2dha1gvRmtCZTJ0azd0OXU2N25saVZrN3NvMzNJazBNRlNlcEM0bGxyQUw1K3p6MjBIV3BsYnJiWnlhTGZMZkJBYXBRR2FINEp4Und5TSsvd1hhV1I5Tzg4V1NrNkE0dFBiTjcvZXlXSER6OXhaRUEzKytzZGdSa05sajVRMGovTnp5STJ5QmJrbUlhb0hoZmtuVWkrWElhUkVJaXBzS2pHTDJvejRZZHhxNW5EanNrOFo5RFBDWk9pOFludHNxZUI3ZWFZY1hqOURXbmpRTjNKUjdLeW1qMTExZGtGZVFWYUhEQ2tFc0VmeWc1NG9WR0xwb2FIei8yVlByMktQSFVicDdQZU9BMEhHM3FSOTFYNm5rZzJua2gyeFVBZUNZMEsyanNuMjBzMEFGeDJVTVZ3elNpMTFnVm5rOUdtUnNQS01TLzQvS3hmaGx5Q2lzWkFNaXVRT1pxdldmcXcyVDc4aDlJZ3JaZ012bHBVSmk5UXJTb0hWZ0xYVVV1RW5oM3JGeTFaOHdQNk5YaGVQR0lJMUJkUGpHQXowZDgwVjhKZkxHWFREbjNKelhmejBkMjc4Z05hd1V5azcwRDR3MTVSQmFwT29GaGhxdlZXK3hIRlVZMWpoL1hINnc2Vk1pNlB1Uk5GUTVLaTBNak9iQXllbXdOa1giLCJtYWMiOiI4OGUxZjk1YmVmZDQ3Y2UzNTZjNDVhNmNhYTQwNzNmYTNiNzIyOWE5MGY2M2JkMmFmOTRiMGUzN2NhODEzY2UwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjhtSVFpYkYxWEU4YVB4UG5zRlp3Qnc9PSIsInZhbHVlIjoiRmJCbVlEN3dqUUwvaTdkV0xEeTNqZTlsd1hZckttUXo1U2tzMTc3Tlhmck1lVFdQMVhTb2ZhdkxkQjFnaVhYTzVwTHVNbHBFMG9Fa295ZTNkUzFRanc2RkIzVGVPTG1RUVo0M0FQODZtY3hnZ1h2OXhpNi9yTDZQK2hXc3hHeXZ2VFgySmNROTJOcjgvUkZZdzROZUlETkQ1N2xnWXBkMlozS3JsWkRueHI0ZDdyOWdyZy9tRmZJZ2dnaXpBeUx6cEdiVDVEclFVeGpWbFJrdDhmWFpheTh0UGxPUHNZRXpablk0K2tqZWZSczJqS1ZXdmpxWXlLUFFIU0NnbkJodExtTWZIUnNXOFMxNmp2Q1FqTmJSUU8vTHA0aS9hMzdsWmNCa2tON0tiazM5TWovNU1EQU9LV0N0N3BKWTdGL0FtWW9YWmtVbUozTXc1QW41L3Z5Snc2VXBOSWVxZDFIcXNtRWhRWFZjOC9YblptajYvYVE4d04wWVZ0Rm1LbThhRS9JOUlxYjBlaHFYUm5GVG1uYW1RZTBURzlkeHBpOXh5MnZmWUdSSGJISGhQNDRlZmtXM1JLNVFOd1dIODV4OXR4VWhQNk44M1NycnRTL1pKa1NXUWc1RG85bHBZMTRZT01YUWY0RE5Xb3E1ZW5DSHdnMjRReTZVTWM2TzVSRmgiLCJtYWMiOiJhMjVhZDgxNzQ4ZDA4OWE5ZDkxMzBiNTY4YWI5ZTdkZGY2ZjBiOGRlMGJlZWNhMWIyNTVmMDFkOGZmY2Y5MTkwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077894971\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-73248939 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-73248939\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1024534124 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:16:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFmd0V2TGVkK25OakdTbklTOEVKMHc9PSIsInZhbHVlIjoiaUM5OVRmM2xmSWN4QStzQW1XMlJRQmI3T0lMV0psZ0JYdGZKV0cvSXpoN2paeUxCdmU0a1NPZ0t4OTd4b0Zlcm1pNmRzQ1kxNEdIS1E4ZjF4VE5nU3BOYkZHVWhqY0F3Ykd2KzQwOXRWeVlTS2FFSml1UGdJTGZxRFIwU0l4dXkxUmo2TEtvSjVaQ1MxeGZYamk2cktXQmgxMjZvQlBPZ3RnZ1BkUTl2UUdJVVBDczVEdUpqZ1JqV1VpT3N2ZmtFamNqN3FCeUV1VmxkNE1EYWl6S1UxTThDRHNkNkpObE8rWlZrczdoNmlyQ0JiUkd4cHlTK0l5Mjc3WWVLT3hlZWx2aEhIZzdiaGZHblpidnV0MkxkcWpoZlUvNm8zS1JPMVhwem1lVE5Wd1JtZnhlbVIyQnlKQTNCeWZ2U1VBRU8wMnArSWRNYkc0c0JKcEV4UXRjKzNaRkZoYWJ6eEk5aTBqdGg4MUhObEtOT1FtSTh0WUFTRms3d3k5bVdFSXRGbmVBZHlTc2xaWGl4dUtSc2ZOekhDY3A1RURJVVpYNjJSTktXM1ZsQzFNUXRVVTU4VGNNTzhJSDYza0I3U0YzRGxZNjRnQk1hQVdsUEtnODNVbmx0RGZTaDE0V0k4NURraG1EUWxCbzgzUXFqSEZxL0NBNU9FZ2hsOU9DenloUWEiLCJtYWMiOiI5NTYxNWVkNGM4MTk2MTQ0N2Q0MDY0MDBhNGRhZjMzNTViOTQ4Zjk3MTQ3OGU3OTliZTM2ZDdjNDA4YzViMGEyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:16:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlVJTmcrU3NUZkswM2ZpR0g0cXRTc1E9PSIsInZhbHVlIjoiWTBVWGxJQ29zZVZBQ1orQUg2KzBnV1BIV3JrRnBBRzdOQnZHZjI0elFiU1g1eHVzQWNQMjhEajlSdDJ1NWlBT2F2VFY2NnQ3aUozTncwRXBZUWZmTWFZb1JjQlBXOHowTVh4b2NiclhPOXhYMVhqSzRqWlZVb2pvWlJ1WENLOThHVEJjaGVKL2FEZVRDcjJlTjhJQUhBckdVTUg1ZVZFSWRrWWZMeUExWHJGRnhpaXdtZFBNb3AwMXg2Nk5CMmZ2SXVVMlV4RlNMR2FvRy93VVY1NVd0MjFpdVptNnd1S3hJdStiWE03cG5hcThiQ3hpQmJEOXk4NUNkK1ZyblQxYXBGUzZBb3JmVEpWY0J4VDdLVFpRSG5GQU9rd2l0REFYSm1oWkxUUlhEeG11UmJSYVJ1RUtmR1lUY2hoU2pPdTJQSDBoVEo1aXFNdkpDRm44VmpkVTJ4QW5mTmFZYXFoTVp1TGp2RlBVM2wycUZhdTFhTkQrS09zYTNyNExRK3FSdzBhV0UyMUprZWdzSkhvT2NaVm5hZHFuRGpNcjlCaXN4NCs0Wkw2YXlFOXd2S2JRNTIwQXhiVjlHc3NEeVI3TldzN1ZDUy9xOWhTQ0VMUFRzNE1xdStYdDdCdE9GUjA5SS9hVjZWMzdPWUU0QlFUNlczZzdyNmVxOVJYY0d5aTQiLCJtYWMiOiI4YzdjNWM5N2RmYTE2NjZhZjE1YTNlYzllNDM3MDg3OTkyYWU2MGFiNjQ1M2Y2MDgzZjNhZTY3MWUzYmVkMDcyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:16:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFmd0V2TGVkK25OakdTbklTOEVKMHc9PSIsInZhbHVlIjoiaUM5OVRmM2xmSWN4QStzQW1XMlJRQmI3T0lMV0psZ0JYdGZKV0cvSXpoN2paeUxCdmU0a1NPZ0t4OTd4b0Zlcm1pNmRzQ1kxNEdIS1E4ZjF4VE5nU3BOYkZHVWhqY0F3Ykd2KzQwOXRWeVlTS2FFSml1UGdJTGZxRFIwU0l4dXkxUmo2TEtvSjVaQ1MxeGZYamk2cktXQmgxMjZvQlBPZ3RnZ1BkUTl2UUdJVVBDczVEdUpqZ1JqV1VpT3N2ZmtFamNqN3FCeUV1VmxkNE1EYWl6S1UxTThDRHNkNkpObE8rWlZrczdoNmlyQ0JiUkd4cHlTK0l5Mjc3WWVLT3hlZWx2aEhIZzdiaGZHblpidnV0MkxkcWpoZlUvNm8zS1JPMVhwem1lVE5Wd1JtZnhlbVIyQnlKQTNCeWZ2U1VBRU8wMnArSWRNYkc0c0JKcEV4UXRjKzNaRkZoYWJ6eEk5aTBqdGg4MUhObEtOT1FtSTh0WUFTRms3d3k5bVdFSXRGbmVBZHlTc2xaWGl4dUtSc2ZOekhDY3A1RURJVVpYNjJSTktXM1ZsQzFNUXRVVTU4VGNNTzhJSDYza0I3U0YzRGxZNjRnQk1hQVdsUEtnODNVbmx0RGZTaDE0V0k4NURraG1EUWxCbzgzUXFqSEZxL0NBNU9FZ2hsOU9DenloUWEiLCJtYWMiOiI5NTYxNWVkNGM4MTk2MTQ0N2Q0MDY0MDBhNGRhZjMzNTViOTQ4Zjk3MTQ3OGU3OTliZTM2ZDdjNDA4YzViMGEyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:16:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlVJTmcrU3NUZkswM2ZpR0g0cXRTc1E9PSIsInZhbHVlIjoiWTBVWGxJQ29zZVZBQ1orQUg2KzBnV1BIV3JrRnBBRzdOQnZHZjI0elFiU1g1eHVzQWNQMjhEajlSdDJ1NWlBT2F2VFY2NnQ3aUozTncwRXBZUWZmTWFZb1JjQlBXOHowTVh4b2NiclhPOXhYMVhqSzRqWlZVb2pvWlJ1WENLOThHVEJjaGVKL2FEZVRDcjJlTjhJQUhBckdVTUg1ZVZFSWRrWWZMeUExWHJGRnhpaXdtZFBNb3AwMXg2Nk5CMmZ2SXVVMlV4RlNMR2FvRy93VVY1NVd0MjFpdVptNnd1S3hJdStiWE03cG5hcThiQ3hpQmJEOXk4NUNkK1ZyblQxYXBGUzZBb3JmVEpWY0J4VDdLVFpRSG5GQU9rd2l0REFYSm1oWkxUUlhEeG11UmJSYVJ1RUtmR1lUY2hoU2pPdTJQSDBoVEo1aXFNdkpDRm44VmpkVTJ4QW5mTmFZYXFoTVp1TGp2RlBVM2wycUZhdTFhTkQrS09zYTNyNExRK3FSdzBhV0UyMUprZWdzSkhvT2NaVm5hZHFuRGpNcjlCaXN4NCs0Wkw2YXlFOXd2S2JRNTIwQXhiVjlHc3NEeVI3TldzN1ZDUy9xOWhTQ0VMUFRzNE1xdStYdDdCdE9GUjA5SS9hVjZWMzdPWUU0QlFUNlczZzdyNmVxOVJYY0d5aTQiLCJtYWMiOiI4YzdjNWM5N2RmYTE2NjZhZjE1YTNlYzllNDM3MDg3OTkyYWU2MGFiNjQ1M2Y2MDgzZjNhZTY3MWUzYmVkMDcyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:16:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1024534124\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-184730251 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-184730251\", {\"maxDepth\":0})</script>\n"}}