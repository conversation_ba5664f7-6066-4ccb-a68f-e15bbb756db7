{"__meta": {"id": "Xb468c685701ecdcce4cc0639de09175c", "datetime": "2025-06-17 15:31:35", "utime": **********.131212, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174293.46902, "end": **********.131244, "duration": 1.6622240543365479, "duration_str": "1.66s", "measures": [{"label": "Booting", "start": 1750174293.46902, "relative_start": 0, "end": 1750174294.956675, "relative_end": 1750174294.956675, "duration": 1.4876551628112793, "duration_str": "1.49s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750174294.956695, "relative_start": 1.4876751899719238, "end": **********.131248, "relative_end": 4.0531158447265625e-06, "duration": 0.17455291748046875, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44962784, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02344, "accumulated_duration_str": "23.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.072493, "duration": 0.02172, "duration_str": "21.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.662}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.107538, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 92.662, "width_percent": 7.338}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  8 => array:9 [\n    \"name\" => \"tea-شاهي\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"8\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1818128113 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1818128113\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1632318649 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1632318649\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1525891092 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1525891092\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173975623%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlkzMlVVSm96bzQzSW1zZ2tzUTlENXc9PSIsInZhbHVlIjoiak1jcWZEbnJzalp0cWZucjdxRHo4YzV1bWhuYjRVS2VvMnpoWGxjWDFkZ1UzdVR1Vk1EUWU5RWxmSHB5emdHLzZrTFdFUVNabHRpUjlmOTRMN1J1ZC9qVHE3b09WNlhSaXoyMkNVR1c0ck8zdXVGT2VCdTZCWXM0SFlCQWpWRHYrZktzVSs1cENHaVorYXV1eVZZaXZVeDljd1hzd2pzWVdnMjlZTXBYdXRTTlY5WFAyMFExWklpdmk3Z3l2RGZyOFIrTnArUGtwNXBwM1BkS3l2QUVack9nY0gzd2R1QXN0ZFB5OEZmd1ZMOU52Yng3bloxWUhqL1BhYTlFREZzRGZjWHlCd0VYWGRUZkNkUU5DcjBiVWM2b3pxSHpYM0dIWDdmWDlPby96QmxyZ1lrZEhjcnJRa0Fodmp0anV5T1VRU2dHcUhLK2oxaVJybnZsczRnVlllUjdpYklVV0s4WkQ0NDlZd0tOM09wNXhBWnVKZllsSmlqaURLWDBORnh4WnJ3c0EyYzhSb0pIOWQ1S1RjdTFCMU9LRFYySFFzR0NVT3d3VGpUWFBwR25YWlgzTC9DMWZBNTZTT2RFV29acUFvR0UwVE50eGZUSkJhY2RhM3B5Y01yTC92VENRSU5OZFlCek4xMVp3K0tNM3RQKzBmQkJFd1FQUE1WUUV4OVMiLCJtYWMiOiIzYTA0YjU3YTkxNGQxYWYyZmYyNjU0NDcxY2JiOWM3OGZkZjJhMjJkZDNmM2ZkOTc2NjI4MTk1MjY5ZWE1OWE4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjdpbmgzKzdZU2J4cVpTWmlNVE1lQVE9PSIsInZhbHVlIjoiYmk4RC94d1MzNWxFUzVCOUJrdVhpQk4yRFNtem0xTXZlaFJLaElIbGh3SVFUQkx5YXdJMFZ4WGR0VTBwTUh0S3hMMkFWenFXVkw3QytRMzhjUWhqejExZzJZL0s3ejZPNXZENzVkM1FMSkZtVHQ4MVB5dE5aR011Nys4c28rdzEwZDBqSUVpeEJKZ1Rocm00bjlWUEd6SVRIOHNyTWVlcnBHOVQxdGlLTzJxbFFzcytNdTNDeWZvUHBpdkNFRUNseDJ1TWU4U0ZvNTdDRCs2NVEvdS9pZDI2Z25qTmRaNWx1SHY1SUVlYUpoM3UrL25BZmViNGM5MWZIekp1L3dsM2FWeWNTZ0pWRVM4SjhrTVhmSVNEdjdWNmNFcHF2VUdsMmoxMFYrQlVtZW9nTmc1Q1ErRktybjdmcW9TbTZueUo3dndKcWtFVUlvY2NmbnpsQWtEbzUxZG45bnhTSTFWZjJSRDh3ZG9RbEhBa1p0KzdKL2g0dHdBTDhxUmhXTjArZU1ZWm1uQk5UNjlZb0oybE90TDhZUmM4bnBOSDFoOWtpN3FoMmM2WG9KSmgvSEFRdm1pWUp3aFlnekRBb3FNR2dHay9HVWpwQUZmRUJITVl6NTZEdFM1Z1FYZk5BZE1PTlR1eG9lOVlsSG5zTHNFdCtoeTlHNVRVTmgyRTdTQ04iLCJtYWMiOiJmOGRmYzYwNzc2YWFmNzE4OGI2YzhmYzk4Zjc2MTFkYTU4YmY4MDZjNDBhM2QxYmUzNDk0M2M1NmU3MjQwYzc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-196530561 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-196530561\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2016717858 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:31:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjErWFBlZ0s5aFNoSDFYY1krODVWVVE9PSIsInZhbHVlIjoiZEIrMi82Q1pDRzlkNFE0VE0zMDhVVU45eVVCNS8vK2JCR2gvaHlRZzBLS1VKRUVtMHVqWlljcWRyZDdxVjVoYnBPaUpiaUxTLzNrcVBZcWhMamxKUXB4ZFpqdHc0aTRVOEdmU0oyN2RoQVdlVjdxc3JWeTNZQ21RUFZtZjZEb3pObldyckdGdUh4bk52VC9JVzJLNE0xWGJwQ0hSTjBZdE9NRzRpaHZJMmRDcCtuMFF1TjlGcnNkeDZDVWc4NUV6RGNVWjJJcERpWGo0QnVJSEt4dThRVStHRlRiclZuYzF0eUZqeUxBV29SRjlBbXJDcGpDcUtoeEZPYy9DVytVT1ZJWWV0RTNiVEdMVFU4cjhwREVoaktSMCt1WWpEY2hjQnBJU2s2bGF6QXF2OUFvY2p2WDIzamkvNHZEMnU0M0x3MTRKL1g5M2YxdzRhamxNUnFPcm9ZWHVnMlRZSG51OFg4eWdrRVBoelh6c2t1SzArakttSlBvcUxtODZXeUxuRzZ5UU5lUVhyNFpFL3ZPcUw2R0V0YytYS2FZNytKT3JNTWgrTjNqK2NwSkVtRmZPRVV5UXE3eXRyajlTOXBNbnZlOExST20zL0tWM21rTnJRcjNQdG9rVnMyVzR5M3BjZjVCcS8yMDlBSGQyUlJtb0M1djlUUUU0TUJrTHZxRTkiLCJtYWMiOiJmNGZhMjYwZDkxZmJiYzQxZTllODc2ZmU4ODA4YjdhMDY0YmQ0NTQwMjc4YmE4YWViZWMyNzFiNTZlZWU1MDY0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:31:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlpQM0ZpRGY0WWJUYVlGZTFhM3F4WkE9PSIsInZhbHVlIjoibUpraVBmbDZLMnFGZjhZQzAwYkhUMzVBRktJTUxSSzJSUWMwM0d0WStHaUNmaWRybmsvOGlTQXUzSnlCcHBySVZURWRxWXV6eFZCY2dCYThLc3M0akRrdUVqTk0wcGZ1L1ZvTUlGZFFabE8zYk01WC9icFZvaWhuVXp2T2d2VU5rRDFnUmR5S2Jnam16aDVZbVU1Q0FxZUtQLzhhQStncHNVRWQwWEhqMEFpbjJ4WENBSGo4bjB3NGZzQkdETGorMkVLQkVxSzVPTlVVb3pLSWIyb2JyRjJUMVk2YTRUcmRrZlN6a0ZEU21XUUVrU3lBZE9QUm5ONHVEVlI0eWJoZmdhb0c5YjBsRXFNRmd6ellTK3dERjhaYjBxUER0UWZvTVdLdTM4OEFwY3l2eU92VVZjTUw2WXdXVGVNZmtVakw2S2VyaE5tOVQwYmR4TWU4M0lRZWpEa3QvS05qL0ZtbjF3UUNobGh1YVh6ZWU0aHlRbVl5VVkyamRONjlsTWg4Nmo1V0tPRS9VYy84SHp6TldrQndVbWtxUS9sMU5xWDY5cUc2T2dVQTNJWVRHVGYxZHcwejgxemYrSXV0UEJ0TGFYS2NEcXFUelZMTW5peUhYUE5iV1pZNHJoTktNTzJ2Y1kzT2dtc3dRaDdRdzhRTlBDYXVacGlGS2xaalFQZS8iLCJtYWMiOiJlZDIzN2EzOWRhN2RmMDY2YWI4MjdiODM3ZGQ2MDRhNTQ4ZTVhZTQxZGUxN2IwYzdjNDNhODQ4MDIxNDIzODhjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:31:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjErWFBlZ0s5aFNoSDFYY1krODVWVVE9PSIsInZhbHVlIjoiZEIrMi82Q1pDRzlkNFE0VE0zMDhVVU45eVVCNS8vK2JCR2gvaHlRZzBLS1VKRUVtMHVqWlljcWRyZDdxVjVoYnBPaUpiaUxTLzNrcVBZcWhMamxKUXB4ZFpqdHc0aTRVOEdmU0oyN2RoQVdlVjdxc3JWeTNZQ21RUFZtZjZEb3pObldyckdGdUh4bk52VC9JVzJLNE0xWGJwQ0hSTjBZdE9NRzRpaHZJMmRDcCtuMFF1TjlGcnNkeDZDVWc4NUV6RGNVWjJJcERpWGo0QnVJSEt4dThRVStHRlRiclZuYzF0eUZqeUxBV29SRjlBbXJDcGpDcUtoeEZPYy9DVytVT1ZJWWV0RTNiVEdMVFU4cjhwREVoaktSMCt1WWpEY2hjQnBJU2s2bGF6QXF2OUFvY2p2WDIzamkvNHZEMnU0M0x3MTRKL1g5M2YxdzRhamxNUnFPcm9ZWHVnMlRZSG51OFg4eWdrRVBoelh6c2t1SzArakttSlBvcUxtODZXeUxuRzZ5UU5lUVhyNFpFL3ZPcUw2R0V0YytYS2FZNytKT3JNTWgrTjNqK2NwSkVtRmZPRVV5UXE3eXRyajlTOXBNbnZlOExST20zL0tWM21rTnJRcjNQdG9rVnMyVzR5M3BjZjVCcS8yMDlBSGQyUlJtb0M1djlUUUU0TUJrTHZxRTkiLCJtYWMiOiJmNGZhMjYwZDkxZmJiYzQxZTllODc2ZmU4ODA4YjdhMDY0YmQ0NTQwMjc4YmE4YWViZWMyNzFiNTZlZWU1MDY0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:31:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlpQM0ZpRGY0WWJUYVlGZTFhM3F4WkE9PSIsInZhbHVlIjoibUpraVBmbDZLMnFGZjhZQzAwYkhUMzVBRktJTUxSSzJSUWMwM0d0WStHaUNmaWRybmsvOGlTQXUzSnlCcHBySVZURWRxWXV6eFZCY2dCYThLc3M0akRrdUVqTk0wcGZ1L1ZvTUlGZFFabE8zYk01WC9icFZvaWhuVXp2T2d2VU5rRDFnUmR5S2Jnam16aDVZbVU1Q0FxZUtQLzhhQStncHNVRWQwWEhqMEFpbjJ4WENBSGo4bjB3NGZzQkdETGorMkVLQkVxSzVPTlVVb3pLSWIyb2JyRjJUMVk2YTRUcmRrZlN6a0ZEU21XUUVrU3lBZE9QUm5ONHVEVlI0eWJoZmdhb0c5YjBsRXFNRmd6ellTK3dERjhaYjBxUER0UWZvTVdLdTM4OEFwY3l2eU92VVZjTUw2WXdXVGVNZmtVakw2S2VyaE5tOVQwYmR4TWU4M0lRZWpEa3QvS05qL0ZtbjF3UUNobGh1YVh6ZWU0aHlRbVl5VVkyamRONjlsTWg4Nmo1V0tPRS9VYy84SHp6TldrQndVbWtxUS9sMU5xWDY5cUc2T2dVQTNJWVRHVGYxZHcwejgxemYrSXV0UEJ0TGFYS2NEcXFUelZMTW5peUhYUE5iV1pZNHJoTktNTzJ2Y1kzT2dtc3dRaDdRdzhRTlBDYXVacGlGS2xaalFQZS8iLCJtYWMiOiJlZDIzN2EzOWRhN2RmMDY2YWI4MjdiODM3ZGQ2MDRhNTQ4ZTVhZTQxZGUxN2IwYzdjNDNhODQ4MDIxNDIzODhjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:31:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2016717858\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1853741102 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>8</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tea-&#1588;&#1575;&#1607;&#1610;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>8</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1853741102\", {\"maxDepth\":0})</script>\n"}}