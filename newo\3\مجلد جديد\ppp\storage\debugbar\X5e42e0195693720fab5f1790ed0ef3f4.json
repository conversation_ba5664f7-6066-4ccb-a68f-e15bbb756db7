{"__meta": {"id": "X5e42e0195693720fab5f1790ed0ef3f4", "datetime": "2025-06-17 15:25:24", "utime": **********.961922, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173923.527821, "end": **********.961959, "duration": 1.4341378211975098, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1750173923.527821, "relative_start": 0, "end": **********.799232, "relative_end": **********.799232, "duration": 1.2714109420776367, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.799254, "relative_start": 1.271432876586914, "end": **********.961963, "relative_end": 4.0531158447265625e-06, "duration": 0.16270899772644043, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46106456, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00777, "accumulated_duration_str": "7.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.887795, "duration": 0.00521, "duration_str": "5.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.053}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9192991, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.053, "width_percent": 15.701}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.939253, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 82.754, "width_percent": 17.246}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6Inl4MXp2Y2FLRTBiOWFjS2N6a0FqT0E9PSIsInZhbHVlIjoiZUR0bFdsbzBsSitlSE90bGVMTDQ4Zz09IiwibWFjIjoiM2Y5YjNiNDM3YTBiZTFjYThiYzBlZTk1OGE5MjFjNGNjNGI2NmNmOGUyYTAxOWU2MDk2ZjIwOTBkMDliOTJhOCIsInRhZyI6IiJ9\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1222079709 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1222079709\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1516304827 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1516304827\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1109817090 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1109817090\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1294556692 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Inl4MXp2Y2FLRTBiOWFjS2N6a0FqT0E9PSIsInZhbHVlIjoiZUR0bFdsbzBsSitlSE90bGVMTDQ4Zz09IiwibWFjIjoiM2Y5YjNiNDM3YTBiZTFjYThiYzBlZTk1OGE5MjFjNGNjNGI2NmNmOGUyYTAxOWU2MDk2ZjIwOTBkMDliOTJhOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173551910%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRnMm9NcGpMKzRDU1FNT1JmbUUyUVE9PSIsInZhbHVlIjoickVJL1JTYlluZjNYWWpZWFV5OTJBaWZnZzZZZlBXVDRXSktSNjhrTUZLRG9YdDErZUxBWnZoY2VLTXZScHArMFFrZ0s5NzQ4TmhGMHBRRFlKT2xnaGNZWnZpU1Jxdm5TUTEwbTFMbzhCMUFlOXlpdTdCTlFhdi9GbndIRXdFU3ZNcWpBZklmOEpVcVJMQW0rMWhjTE9mWHNSWTZnSE1EdTBEQUJpZ0l2cTdXc1lKR1NzSE8ra3BuNkttSnhKVG5hbUtQSDJDTzRWMjU1dHN4MzNDSm9rbGhLaGdsNm0vYXp3ZE1vZXA5V1d1TmZOb0w0a1dFOTJ5S0dvTGNKQUVhSkkzYVEvYWRnRXBzdWRWTFFSRVoxNjdrZjRSSG9QM3h6eUZ5d1J2ZUpZYnh5c1prZHFIYWRRTk8yZmF0L3pHMUZUL1FhTWZ4ZEhPYjR3MDl5cklrOEJicXM2c2Rub00xRnlTMXJkOCtCK2hURm12c0FNMExmVkMwMDUway9xRktLdE1DUGtjaHI2VU1WNkswK2hhb1g0N0dEYXpiRG9sZDRtOElNMTd4UFZ3RER2d01JM2NLcEJpaTFKRE5EZ283aHlzQzJudGttc0pCQ2FjS3lKTXp5N3lOLzVwZ0tSWW5jbXJuc1Y3b3RZUERCdmVNSkRMdlpZNkRLNlNXTzhqQUciLCJtYWMiOiIxNmI2NDdiMTNmN2U2YTJjZTNjMWU4Y2E3MTM3ZDNhMGEzYzRiNjQ0NmIyM2NkY2M0ZGM4ZjhiYjM1NDI1YjhiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZPQ003U2JkVzBRQjFoODZTdng2c2c9PSIsInZhbHVlIjoidzJ2T2hFWlRkZ05RMEFBR3BXL1N5Mk5EbUtIWXFsQmpkOEpBcERMNlM3OHAycFNxVnFjZTIzdGE0V3RoeEhqYTlpRXdhMDV5cHJ4M3loNmlOQjRSalZNMnc1WndRWHQ5UXNLbjlOUFZBSmZ4SjlGeE5mRzdKWThKcE1vWSt4bzFwYk1rWG1KTks2ZXZPMHZITFI5L2Izek9jUVlhNlBsMXo2cWtMTnJXZXYwK2tpMVRFQ1U0bXFaOHNtV1pRcStEa2VRQjdBOHRBMmltVUhPT3dMUU0vZ2NGOS95d1FTQjlCVlhIK3loU3VQaE9TSm00bzdFUHBBUHV1cWxZcjI2c1ZZSENmaFpCQXdVbHlERjJpQ21qcmQzSWRmeEt5MUxpcnRpYitmVnU1Tkp5QWRWQ1ovTm1yL0VyT3pRQlNqc0FEbzYyM1g1VmE4bi9nUVFwc1lKUEtMSURHVHlvdS9HOFFqUWhHV1RtRTJQcEdqZzkzSjV0b01LMWM2a09MZmQwMTYxU0R5aWwrSllZMUM2NWtrTUZJYlhNTWRGeHBKaDQrb2tRM0k0Y3N2VHV1b3cxek9YRzJEZVF4M1ljektzN204aEg2eW9DaUhlNHUvK25BR2Z4bllYV3ViK01rWjQ5dUVxWEJWTFhVMnRnRDBQbUFGTElIVXJrNTBEd2RscE0iLCJtYWMiOiI2MDBlNGI5ZTAwYTE0MDEyNTNkMzU4ZmMxYzBiMDRmZTdmYWJhNGZkMzU1NGY3OTkzMTk0ZDNiNzc5M2NlN2FjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1294556692\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-920490444 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920490444\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:25:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilh4cDMyQ3VrSlhlbEN6WnJrSmNVSUE9PSIsInZhbHVlIjoiZzZiWHNrV2xSMHlueHJnR0lyZ0JSZHRLdEtWWmttNGROTnA5d3VEeXdXd0c5RlJ2WVN2czB1Q0RDeEs0ZmR6eTBidjFuNGN0OVdWdHoxSVhscFFXWXIzQ0tXSlZUSUp0czJDRlJYZ0k1RGtWMGthMng3VXJyQjR5bnkyOGlaV1REV3BFTlo4L2lJK3VwNWlobTBJRVN2UVMyUHRhTHBINE40NDhyWkZ0aHJqSEZCWm1PYTRaOFdpNnhJNmx1R2Z1UVBJcGlvVVQyajNqQTRwaXk0NUVHVmtjd3dTT1JiOXhCbVJKM3JINzNReHRIaVJPUGFlMFdDOGw1NlBNODRXaXpaUXFlSHAvdnFGSVM1Y2hhSlkyNUZsYVhER1VPbXp0b1dUaHJXZzFqeVo0VkEzaENIRDBPbXZLaUttRjZLMUE1dEVFQmdGbjh3aytQbDVCNHVyR1NoYnV3bE5sdlVCUnZnSytYN0dSeWlXQzc1cTQ0R2VMUDBPdEUvWEd2bHJycjM2Q3E0eXFJU1RYRWxNQ0NHVDJSOGpUS2Y0ZmxRcFNtdHZHWE1nMmZBeEZEV3liRy91Mis1MVAweUFlbHpYU3RKU0E3dWhjbmZVcFYyNFpGY2RmMGdTUVk5a0hTMHRQdXY1L0dkaGUrS2R4ZlpVVnc5UmFSbEZBeXplMjZwRFIiLCJtYWMiOiI4ZmY0ZWYwODMxYjc4ZjllMmFlOTFkM2U5YTFiNjdhZDFlM2Y4MGVkNTQ4NTEzNmJjNGY3MTEwZDMzMTNjMzQzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:25:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik1XbS94ZWpONEI4cDBBQ1BMUW5sNnc9PSIsInZhbHVlIjoic0UrTWx6NzBMUmNaRDR3aTVEdnVMOTY5WGxzNlZVa0Fod2ZXeGllbkRrajVGbE1vMVRWUUtEN3EzUWVCTEducG9vSzc0RXBpaG1JSE1yb2IwNVU5emZ5LzVodGxpaytIRVlSUHZreDFsOGJuV3VwY2JGN1FsOCtDcWxNd2dQSXFNSTAyL2xMQ2xSczhYS1o3V1RGbmd0Slc1T3ROSlJjVGRzYzFSbUZzTW1ZVk0vQmxuQ2xtdXJtM1dCZnlWQ2g3VHhaUHg3QlFMalJKT29EbzFIQmM2cFJDbVlpWXUyUTVyWmwxQkM4YXgxdVFqVnZoTE1DeVdsTHl2eDBwb2YrZjczazN3VFRuT2UyejJtTkNtVnQ0ZWJDZ1N2SlJhTDFONDYrUFRiODUwYS9DWUo1MlpOd2J6YnlGRHNoRDBlNUl1SENXYnZKMXBzTnNrelVYanpZdVlIWXV6elVkNDNCeVZScWkxTXNZTE92UVlDY0pIY2MzRzU5cFVmTlJKSWs4N3BlNnRWdi9zajcvMW9Zd1JKRS9jN25scjBXdlYrMGlUU2xtMFJ1MFd2WmxkWVo0bWdyODNva3U4dXVzc1ZCeUpWQ3liaXRSOVVmTERiRG1qelB5cnIvcWtaRmhqSkN1a1JaOFM1SktQS3cxVG9jRFhBOVNXMzBwSHZDazZxWkQiLCJtYWMiOiI4N2JkOTg4MTgyNDJiNTU1YzYyNDFkNGYzY2ZiOWE0OTVmOGQ1Y2UxMDIzNjI4ZDExNmQxZWRmMWRjYTY5MjRiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:25:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilh4cDMyQ3VrSlhlbEN6WnJrSmNVSUE9PSIsInZhbHVlIjoiZzZiWHNrV2xSMHlueHJnR0lyZ0JSZHRLdEtWWmttNGROTnA5d3VEeXdXd0c5RlJ2WVN2czB1Q0RDeEs0ZmR6eTBidjFuNGN0OVdWdHoxSVhscFFXWXIzQ0tXSlZUSUp0czJDRlJYZ0k1RGtWMGthMng3VXJyQjR5bnkyOGlaV1REV3BFTlo4L2lJK3VwNWlobTBJRVN2UVMyUHRhTHBINE40NDhyWkZ0aHJqSEZCWm1PYTRaOFdpNnhJNmx1R2Z1UVBJcGlvVVQyajNqQTRwaXk0NUVHVmtjd3dTT1JiOXhCbVJKM3JINzNReHRIaVJPUGFlMFdDOGw1NlBNODRXaXpaUXFlSHAvdnFGSVM1Y2hhSlkyNUZsYVhER1VPbXp0b1dUaHJXZzFqeVo0VkEzaENIRDBPbXZLaUttRjZLMUE1dEVFQmdGbjh3aytQbDVCNHVyR1NoYnV3bE5sdlVCUnZnSytYN0dSeWlXQzc1cTQ0R2VMUDBPdEUvWEd2bHJycjM2Q3E0eXFJU1RYRWxNQ0NHVDJSOGpUS2Y0ZmxRcFNtdHZHWE1nMmZBeEZEV3liRy91Mis1MVAweUFlbHpYU3RKU0E3dWhjbmZVcFYyNFpGY2RmMGdTUVk5a0hTMHRQdXY1L0dkaGUrS2R4ZlpVVnc5UmFSbEZBeXplMjZwRFIiLCJtYWMiOiI4ZmY0ZWYwODMxYjc4ZjllMmFlOTFkM2U5YTFiNjdhZDFlM2Y4MGVkNTQ4NTEzNmJjNGY3MTEwZDMzMTNjMzQzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:25:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik1XbS94ZWpONEI4cDBBQ1BMUW5sNnc9PSIsInZhbHVlIjoic0UrTWx6NzBMUmNaRDR3aTVEdnVMOTY5WGxzNlZVa0Fod2ZXeGllbkRrajVGbE1vMVRWUUtEN3EzUWVCTEducG9vSzc0RXBpaG1JSE1yb2IwNVU5emZ5LzVodGxpaytIRVlSUHZreDFsOGJuV3VwY2JGN1FsOCtDcWxNd2dQSXFNSTAyL2xMQ2xSczhYS1o3V1RGbmd0Slc1T3ROSlJjVGRzYzFSbUZzTW1ZVk0vQmxuQ2xtdXJtM1dCZnlWQ2g3VHhaUHg3QlFMalJKT29EbzFIQmM2cFJDbVlpWXUyUTVyWmwxQkM4YXgxdVFqVnZoTE1DeVdsTHl2eDBwb2YrZjczazN3VFRuT2UyejJtTkNtVnQ0ZWJDZ1N2SlJhTDFONDYrUFRiODUwYS9DWUo1MlpOd2J6YnlGRHNoRDBlNUl1SENXYnZKMXBzTnNrelVYanpZdVlIWXV6elVkNDNCeVZScWkxTXNZTE92UVlDY0pIY2MzRzU5cFVmTlJKSWs4N3BlNnRWdi9zajcvMW9Zd1JKRS9jN25scjBXdlYrMGlUU2xtMFJ1MFd2WmxkWVo0bWdyODNva3U4dXVzc1ZCeUpWQ3liaXRSOVVmTERiRG1qelB5cnIvcWtaRmhqSkN1a1JaOFM1SktQS3cxVG9jRFhBOVNXMzBwSHZDazZxWkQiLCJtYWMiOiI4N2JkOTg4MTgyNDJiNTU1YzYyNDFkNGYzY2ZiOWE0OTVmOGQ1Y2UxMDIzNjI4ZDExNmQxZWRmMWRjYTY5MjRiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:25:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1609391752 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Inl4MXp2Y2FLRTBiOWFjS2N6a0FqT0E9PSIsInZhbHVlIjoiZUR0bFdsbzBsSitlSE90bGVMTDQ4Zz09IiwibWFjIjoiM2Y5YjNiNDM3YTBiZTFjYThiYzBlZTk1OGE5MjFjNGNjNGI2NmNmOGUyYTAxOWU2MDk2ZjIwOTBkMDliOTJhOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1609391752\", {\"maxDepth\":0})</script>\n"}}