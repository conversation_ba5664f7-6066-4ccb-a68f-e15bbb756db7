{"__meta": {"id": "Xefc2bfb5d29d856532256049752e7c88", "datetime": "2025-06-17 15:25:49", "utime": **********.405636, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173947.81748, "end": **********.405667, "duration": 1.5881869792938232, "duration_str": "1.59s", "measures": [{"label": "Booting", "start": 1750173947.81748, "relative_start": 0, "end": **********.216999, "relative_end": **********.216999, "duration": 1.3995189666748047, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.217023, "relative_start": 1.3995428085327148, "end": **********.405671, "relative_end": 3.814697265625e-06, "duration": 0.18864798545837402, "duration_str": "189ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46330496, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1696\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1696-1706</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01892, "accumulated_duration_str": "18.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.332129, "duration": 0.01763, "duration_str": "17.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.182}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.381044, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.182, "width_percent": 6.818}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1948921140 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1948921140\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2076751862 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2076751862\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-58941058 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-58941058\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-510523978 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173924302%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdMbVJXN293dUFNeDVyUXk5bmZlWnc9PSIsInZhbHVlIjoiMlJQSlNMR1BxVm1rSEw0UjhiOEljYi94V3p6cmo2Z0J6YUp3UVNzN2lGb2FKejBKcFY1NVZNNXErUTVYQ1F6NVA3ejl3VVczRXU5dFlib3k3aUI1YUU2ZVNKbjhTTlpqeTI3RzBHakw2K21jYW9yRHg4NVlERWxRUXJTblhpS09abG1QMXY1RmUvQUxzOG5JS29VbVZwaFozQ1lkR1BDb0lWVmthNS9POWNFTjZMZ1BhN09HSzVLM0hBT0ttSHp4b2hldWg1akJ6WitxVklCQXpuZERjSllKQm4zQzhpeGZMdGhFN0czRHRwU0RXRXNtTys3R1E0Z3NlR2JaRzlFWHA0bmsvNGJlWXgrR21FdnBjT3R3RUlMZjZJQks3ZGpWem9NUzdoZ0NsYVNkMmJnajVjQU82NE5Fb0FXMXhPNUg2RE05RlAxL255YjBsMUoyeTZvT01PeFZldmNLSDEwS1lSSU1UTE42TkZMZHdTdnhUQi9NelhaOTcyZjVMOTJqY0JVZnRncnBHeXd3K0RZOVBrM2gweU0wZy9MeE9NbHJPNk5ib21ERWt1WnZDbDZNMWpreGVIanBiRGhxdFgrcURGWGU4WVhHUjduVjduK01taGdlR093Z3QrYm54UXRSdnF6eDZrK0drQjk0Z3gxYXNwWU9rclV5dUVsSFlXRlMiLCJtYWMiOiIyZDc3MjVjZDNkYTJlN2JjYjE0YzYyY2JkYjM1N2VmZTFkNTBkYjlkNWRmMDI3NzlmYjE1OWJjNmE2YWZhNDMyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InRSRENuNlB5eHQ5a1dsRmhGMmFzRlE9PSIsInZhbHVlIjoiN0FuNDNRNVUwZnl1Umx3QmpSaEtzRDlUNncwSUluTGswaXYzOUR2Y1ZYYjgyWW0yODJHUm1JUkdjQ1VPSlNZTWdiMFUyYVpneU56RVVpTXIxZ2FLbkNPUGFwL0FRRnhPNzcvejMvOXFnK0Y5ZFd4TGNLMnB5LzNpbGhpcVNGc1NGOHdhL1I2OVd1TkhtalR4cFdPV3N0RzBCWVNlelBRQzNPRnM3Rkl0T20zUDVKU3kzanJGN3BsQ2h0U0hDc0Y5WHl3Tkkwb0VaaVJuQ2t5SDVrenF2QmQ5WUVxQ1B6bTFWT2JBbW1ZdFR2QlJKVHhzdGJwQ2liZ0pVY3RQYUdGengwSmU1b2RrMHRiN3FsNTRLV3oyNk1IY29BWjdyUEYxRURJa3hYVTVpeWJKZVhpZ3VUREd1UUIwa0NQUjFwdXRKSU5nZkhJOVdmdnh0STJvU2xuSlhpZW5kam9PVVNpSGd2SnJEUHZXcEtNTlNtWGRaZ1VwTFpzQTU1Tm5LU2tYMTBYODFnYmNuM3FGZndzT0R4UFpaOWJvbFRlSkhsWFJHWldXMW1CL3M4UDBDMVY3RFErM0pmWU9ERzE2bG9ieXlJL0EzN294OTFSRWRacHdwaXFqZk80WW92a0JqNUU0ajI4OXVHa2lZNnV4a04vd2wrNzVLenVtK3Y0MnFDbnoiLCJtYWMiOiJiYTg3MDUzM2E5MzBhMjE3N2JkODEzNGVjOWI1N2RjYzg0ZGMwOThlYTFkMDE4ZjcxNmMwMzQwNmY3NDBiNDAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-510523978\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1524183192 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1524183192\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-143632644 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:25:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRYMHlLZjRwNTZVVFVZcit6VHFWMGc9PSIsInZhbHVlIjoieUEzTzErWExESEJwOUp3bi9MZHRBc3RMaWFpS3VyV29jUnhRTGxUcTB3ZVRjZ1JPcGxkS0lJVThoRXdiRS92b3UzK0tJdUE1QVAvOTBSRlZjNVFkVU5VMEs2OG5zc0dVNzlZYnVVNzJQQTVHalVVb3B2akZvZmFnaklpNDEzSW10MjVNT3kxdzkrbzdUWFB6V0YwTnFKbkNreWFDNHAvOFI0Ry9RTkR0a1pJOEtTdnppNEtSQ1VZWUtOZEluTlRWZUdybHNqenBPanRMTTJ1REpYSm1FQjhQZlV6OG9La3FnTGdvejNLL2NjNEZteWVYU21CU3hPYlRKTU9jR0pMbGtuT01lU1puL0dZaGJHdXRiRENmeEwrREN5MVJjUVVMY1FzQzFwUWFHRkJDVUM0NEthMWFFa3FzQkNtdGtaamdRa0wybGt2enlNS3VOYW1qVTB1YzNkSnk0WlM4d1RVYlp4clJsSHp3YUFuT2wveWc1bnZOWkl4QmtKSERsL3R5Z1A2YVVNKzV0bXlmUTZ3cElxREVlUWwwM0paQ29rejcwVitWRmJFdnJkODhnbStyY2hzZWlINTA2emxFNHhJdkxRcFZpVFNsVHhMVUJZaDltM0IveEhUTzFlRFBPVjZXd1RzcDc2USt1VUU5dTBpdUtkVDc3bDBpTFRIelY0YXciLCJtYWMiOiJmYzA2N2RjZWM3ODhlZDRjYmVjNDIyOWNhYTQ3NWJjMTBjMzg4ZWI5MmQ1YzQwZDMwZGMzYzg2YjRhNWUwNDMzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:25:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlFBVnpZY0pvdG5BNjR6YlpqZVA0Unc9PSIsInZhbHVlIjoiR21XeDMvYy9pRzJBKzdUUXRkMlNPZFU1UE04UTVuZWVRYXNmTEVMdHd4eURqcFdOUXppdlJqWDRveFR0QmdENmdZbTYyZVlpczFramVaTjVhV1VLcEk2b0JsMkVYdGo0Y2UzbnpzMXF4bUJBb0FnbUxyS3FyeERHMEE0NFZCOXh6dERyRWVwZ09YT0FQbDJiWVZpS0VqSExaYlBKV09lYm9zN3p6cUxXaVUyLysxK0M3V0VycUIyNnUxam1LUGtwcndOUUk3SnN4S3J1RWVKVTlYWFFGUWZKZURuUnJLUGx0QS83NkxtNHpnNXBGYUlaVngyQmRyU1F4d1dlZGdCekJGY1RkSEFlZXhVQ202WEJxaUlCNzhseVhYY2FtaU1BVzdJNnlrN3hBZ0haWEJRT25BU0JVbE81c20zVW04aGgrV2Z3YUFXZVRLdUFlZlBZSkxSNG05NDFyM3NjRklmQzJ1VzlvZ0RGTnRLTkVYVmliMjdmbFhHd0orZUVhVWhIZC8vY2M3SFF3NURzdVFrSUM4Q3BJK0xqYS9MR2c5SmhxVndRek5CTHB1cTN2eFBCbWxsZnpoRjFOTkplSllsaTZBQVF0ODlpS3YwSEc0WmZjcFp4Y3hJWnF3bW54N1BVL1pCSzRnZk9PcTRlL1d4eDNnanB1LzQzcE9lN1Q1ZFMiLCJtYWMiOiI2OGFiMDNhYTY0OGFlZGZjMDMyZTAwNzE1OTZmMDdiY2NiNzczYTIyMzk5MWVjNmIxMjg5NzQ4ZWNkMjZjZmY0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:25:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRYMHlLZjRwNTZVVFVZcit6VHFWMGc9PSIsInZhbHVlIjoieUEzTzErWExESEJwOUp3bi9MZHRBc3RMaWFpS3VyV29jUnhRTGxUcTB3ZVRjZ1JPcGxkS0lJVThoRXdiRS92b3UzK0tJdUE1QVAvOTBSRlZjNVFkVU5VMEs2OG5zc0dVNzlZYnVVNzJQQTVHalVVb3B2akZvZmFnaklpNDEzSW10MjVNT3kxdzkrbzdUWFB6V0YwTnFKbkNreWFDNHAvOFI0Ry9RTkR0a1pJOEtTdnppNEtSQ1VZWUtOZEluTlRWZUdybHNqenBPanRMTTJ1REpYSm1FQjhQZlV6OG9La3FnTGdvejNLL2NjNEZteWVYU21CU3hPYlRKTU9jR0pMbGtuT01lU1puL0dZaGJHdXRiRENmeEwrREN5MVJjUVVMY1FzQzFwUWFHRkJDVUM0NEthMWFFa3FzQkNtdGtaamdRa0wybGt2enlNS3VOYW1qVTB1YzNkSnk0WlM4d1RVYlp4clJsSHp3YUFuT2wveWc1bnZOWkl4QmtKSERsL3R5Z1A2YVVNKzV0bXlmUTZ3cElxREVlUWwwM0paQ29rejcwVitWRmJFdnJkODhnbStyY2hzZWlINTA2emxFNHhJdkxRcFZpVFNsVHhMVUJZaDltM0IveEhUTzFlRFBPVjZXd1RzcDc2USt1VUU5dTBpdUtkVDc3bDBpTFRIelY0YXciLCJtYWMiOiJmYzA2N2RjZWM3ODhlZDRjYmVjNDIyOWNhYTQ3NWJjMTBjMzg4ZWI5MmQ1YzQwZDMwZGMzYzg2YjRhNWUwNDMzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:25:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlFBVnpZY0pvdG5BNjR6YlpqZVA0Unc9PSIsInZhbHVlIjoiR21XeDMvYy9pRzJBKzdUUXRkMlNPZFU1UE04UTVuZWVRYXNmTEVMdHd4eURqcFdOUXppdlJqWDRveFR0QmdENmdZbTYyZVlpczFramVaTjVhV1VLcEk2b0JsMkVYdGo0Y2UzbnpzMXF4bUJBb0FnbUxyS3FyeERHMEE0NFZCOXh6dERyRWVwZ09YT0FQbDJiWVZpS0VqSExaYlBKV09lYm9zN3p6cUxXaVUyLysxK0M3V0VycUIyNnUxam1LUGtwcndOUUk3SnN4S3J1RWVKVTlYWFFGUWZKZURuUnJLUGx0QS83NkxtNHpnNXBGYUlaVngyQmRyU1F4d1dlZGdCekJGY1RkSEFlZXhVQ202WEJxaUlCNzhseVhYY2FtaU1BVzdJNnlrN3hBZ0haWEJRT25BU0JVbE81c20zVW04aGgrV2Z3YUFXZVRLdUFlZlBZSkxSNG05NDFyM3NjRklmQzJ1VzlvZ0RGTnRLTkVYVmliMjdmbFhHd0orZUVhVWhIZC8vY2M3SFF3NURzdVFrSUM4Q3BJK0xqYS9MR2c5SmhxVndRek5CTHB1cTN2eFBCbWxsZnpoRjFOTkplSllsaTZBQVF0ODlpS3YwSEc0WmZjcFp4Y3hJWnF3bW54N1BVL1pCSzRnZk9PcTRlL1d4eDNnanB1LzQzcE9lN1Q1ZFMiLCJtYWMiOiI2OGFiMDNhYTY0OGFlZGZjMDMyZTAwNzE1OTZmMDdiY2NiNzczYTIyMzk5MWVjNmIxMjg5NzQ4ZWNkMjZjZmY0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:25:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-143632644\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-688024636 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-688024636\", {\"maxDepth\":0})</script>\n"}}