{"__meta": {"id": "X375caa3c8a043f91aa072a0be0fdeb3d", "datetime": "2025-06-17 15:25:56", "utime": **********.98981, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173955.641936, "end": **********.989845, "duration": 1.3479089736938477, "duration_str": "1.35s", "measures": [{"label": "Booting", "start": 1750173955.641936, "relative_start": 0, "end": **********.846968, "relative_end": **********.846968, "duration": 1.2050318717956543, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.84699, "relative_start": 1.2050540447235107, "end": **********.989848, "relative_end": 2.86102294921875e-06, "duration": 0.14285778999328613, "duration_str": "143ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44978192, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.018850000000000002, "accumulated_duration_str": "18.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.940671, "duration": 0.01776, "duration_str": "17.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.218}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.969956, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 94.218, "width_percent": 5.782}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  8 => array:9 [\n    \"name\" => \"tea-شاهي\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"8\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1221032508 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1221032508\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1221466451 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1221466451\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1363973075 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173924302%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhkK2szZFRtN3JBbFZzS2RZK2pPU2c9PSIsInZhbHVlIjoidHFWNnJnT1FwaWpFcVZkNnc5UmZJZ0JJc0VXK2FlUkljY1JkeGVZUDhaeW1uZXpEeDJKOXVrOXg4cFR5VWRBdC96Rm1WK3ZzbGxMV1RURTVub0pmWjVYMHRLVHRSMnh3ZHg1TFNNZkxGQkdOb2VPZ0o2djZNYUNmZzZqYWdmSFBWOWZYbGgvYk5lb3l0VzhNY1hrWmhWdGV1L0dBaTZxa1JTcjZscFlqNVhwdHBCbmdOYmQwMWRjU2NXczh2bVBEeU9qU0wxdTV5cFRCcjZ0V2FId2RvODREWk8xTld4TUJobExrL2pwemJYNGo1NlNwblJZOWRYVGU1TGxkMktkSkF3L2F3TVpHdEpPNUJGbjhlc1dFSC9GMVJUeldjTlJsOWg0RHBtSXpuakpvQzVFd0EwZXU3VlQyd1d4T25mdExQWmgwNkVaNDZXMStlSnBoWEozYUdveElsV1VqK2p0djlXN25sV0JJdUtxakxjMUw0MzM3cWdiMjNJc1BhVisxbnA4UkpRSnRoc1ozYXljaGV3QVJ0cjlIUmNYdWd2Z2xqM0RCU09uZ29VVmdkdTJBOGZNTGdNMHNYd3BLR0FTSFFlUmV3L01ybmtGQnNaNytCMkJoQ1hzckl6eUdJMHF6UWtoUU9zT1RqMGN0Y2oxSzJML1E4WkE0L1lteS85Q3UiLCJtYWMiOiJiN2RmMTUzODMwMGU1N2YzMjc4YmI3MjlmOWJjYjQ2MWNlZjdiYWJkNWRjN2RlNjE1NDk1OGQ4NGNiZTQzMjE5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlJWdFNPNENTeU8xdjlEZ29wQWxGd3c9PSIsInZhbHVlIjoiRVNsVU5YbGRIZ2crbVhPZWJFY00rbWVsZEtua1d3ZUlIWlkrVFhnT1FBbllVY2gwQVZOR3VkSm01SmU4YjErdEwzcFluNTJoRXVIM0l4TTlrckpFUHlXQ0h4a2NYNk5Zdi9WcVJzamY1UlBabEpVVTdXNkZLc3pPZUl4a0xQbTduQUhhMy81YU5hSHdwdGNKbndaTDN2UUs0TlJhMGgwZ2Z0SHdrY3ZxK1FQY3ZTZ1RjcWFQc0pFcFVjMHZtaExxb1Irc2ZtWmlCbDhXOWFrVGpTUmljZm5JdXVkWVd2KzlSWVhUZXJTRzVDUXBFWE9wV3pvSHMxYStjZWsxQ1BIbHk1M3plY3FGMGx5TmhobUk5b0d3S2syS0V4TnpKVkk5bzUwZmgwbGNXSkZEQTg5UU5LMXVhSldQbDQ2c0pNUENCT0dscmpLdTlPR1Bmc3JrTVhyb01IM2JYRE1QYnY4ckdRVVJFY2liZVB2VTZrcUpkblpRMHVFWDlpNVBWSXhuaFB0cmpBYkRwSU1YcmpqNkRoZVlUZm5PWWJ0em1CNWVqSDFENGNWSXUrVldvelFyVnVXREVLM2pvdURrOEN1SnBsUW1VQTdvT0lxZFNkVk90L0hRcmtKTjNpaUFGZ2J1bkxISTBsbU5lZDB5M1NDcjN4cEY0NTN6UGNVTjhZekoiLCJtYWMiOiI5MzNlNTE5NjUxNDQ5MjEwNjY1MGNkNjllNDA5YWFjMzhmYWY1MDE4Y2U4MDdlMTRhYTBlYzIyNDcxODU0MTJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1363973075\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1157645555 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:25:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlQUXhLWURwQjZxNE1lTzVCQU9EOXc9PSIsInZhbHVlIjoic2EvU1M3QWJseHZiaVhpM2FCd1JVOGJjS1l5OWFLanZaUERNUVVvcElMZG9xU3dmRFpsazlOdHVFWW9ja1hDRmR3MTFZam5tL3A0VHRkbTlKUjQ2ckp6M2lPMXZzQ1JMd0t1SkhSVjdnRm1GbFpNT1JlRk8wZk9NbjQ1VDNlMW1kck9EMWFYZ3V4RUtTTk1nUkQwdTkwa3kxQXpZYzhFVExkM25QTzVLV2piY0R4ckxWY3QvcW91NGFFZlZRSDl6aTlFWlFyR3F5NlZ5a2s3dENkbmp6UEszbWxpd0h4UVlpVy9aK21Ealpici9FbERERkZDdDh3cFp2QVlNQnZXRGNIeDNlZGNzdlVybHlwS01RbWxNK0FoRlpCRFdIZ1JkTXNPSFptTGZXd0RoU2h5TzkrTnhkTVo5Vjc3dUY4OEtRbTJqeUtwcVFVK2FxSldPUXJkT3ZKZERMV0JVM1BsUHRlbXE4bjdjSzA0YlB5TXdMVExHc3dCRXpMNzZHQ2F4cjdsbHk2WVRGYlBhV3RZZk9LR1M4Tjk1dDh4YUVMQWlBVHlrMFRPRlEwRFJlUzBvSnlHRHJaQnVNRFBPVjRVdzVXcVFvVFZiM2t3Vmt3Y0wrclo5VklyV1BzVzJUcmRFako2eVNYUThaUTlncFd3VGE4UDhqWUZKcnFNY25KZS8iLCJtYWMiOiI5YTNhNDE0ZDhmMjRiMDgxYzdhMzc0NjMzM2RkNTJlZTk5M2NlMDEzNjA4Y2QxYjllZDNlOTVkYzA4MTk4YmIxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:25:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNOcTRmT3dyVWJHQmFYMjgydG45QUE9PSIsInZhbHVlIjoiaUZUTzVLZ1JHOFZjMGxrZzFzSXFUZnZoeFJVM3hCTitSaS9rQmdvcWpiWUsyaml2a3hmd05XTWNOa1hmN2x5aVVtZXhibUdLZjZMSTRvTFh6MVV4OWlhRFJoeUxXSTRNZzhMRUhHeitRRFNQWThIeWtUUHNMWUl6TWZzMTRidkJKZHBFU01sRCtocW9Ec2JIUGdGeWhOS3NMRGJ5TTFUaFEwMFNyTlVDUFRQQlRRb3I5cXN2dVJsOHZwbjlQWU1Pd0RuK3V2NmZvU2tzVVd5c3FIQW5rSWlNZ3hKeXdlajZWMFJkejBSeDNnN1BvOW5kczhzd3U5NXpNNXFRZlhwQ2VCMUNXWGpXeXpVMkZDVzBCQWlZbVZ0aDJhNXFoVWN4RC9QOUpaMDl1THlTMzlkRWcwTWRaeE1jWFJuMDR1S1VrWjNyb0JCNDVXcjRpMGFsSkUrNktkZk5RTmR0OTlRNE9pbFM2WkxTTHFuSXJzdE5TamxOdk5NSll0NzVvNnFrb2dWT1Y3QnJZQjdaWk53eHE5MFJkdXUwa0NZUVhUK0ZybVlXeGozNW9VbmZiaUVmTUh6ay9kRG5PMXFSQlZiR3NKcENjWHA3ZHd4TkRLTWZXdVZWVmFuVFV2eXhBVzZRN1JUM0tTem1EUEJFbExXOEx5aW14ZFErSTZVZlArbEYiLCJtYWMiOiJkYzM2Y2RiYTYxNDAyYjI3ZGI2ZGNmOTNiNDZkMGNlODNjYjQ0N2FhZjA4OTM0ZWZjYjA1ZGRlODZlNTk5NmUxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:25:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlQUXhLWURwQjZxNE1lTzVCQU9EOXc9PSIsInZhbHVlIjoic2EvU1M3QWJseHZiaVhpM2FCd1JVOGJjS1l5OWFLanZaUERNUVVvcElMZG9xU3dmRFpsazlOdHVFWW9ja1hDRmR3MTFZam5tL3A0VHRkbTlKUjQ2ckp6M2lPMXZzQ1JMd0t1SkhSVjdnRm1GbFpNT1JlRk8wZk9NbjQ1VDNlMW1kck9EMWFYZ3V4RUtTTk1nUkQwdTkwa3kxQXpZYzhFVExkM25QTzVLV2piY0R4ckxWY3QvcW91NGFFZlZRSDl6aTlFWlFyR3F5NlZ5a2s3dENkbmp6UEszbWxpd0h4UVlpVy9aK21Ealpici9FbERERkZDdDh3cFp2QVlNQnZXRGNIeDNlZGNzdlVybHlwS01RbWxNK0FoRlpCRFdIZ1JkTXNPSFptTGZXd0RoU2h5TzkrTnhkTVo5Vjc3dUY4OEtRbTJqeUtwcVFVK2FxSldPUXJkT3ZKZERMV0JVM1BsUHRlbXE4bjdjSzA0YlB5TXdMVExHc3dCRXpMNzZHQ2F4cjdsbHk2WVRGYlBhV3RZZk9LR1M4Tjk1dDh4YUVMQWlBVHlrMFRPRlEwRFJlUzBvSnlHRHJaQnVNRFBPVjRVdzVXcVFvVFZiM2t3Vmt3Y0wrclo5VklyV1BzVzJUcmRFako2eVNYUThaUTlncFd3VGE4UDhqWUZKcnFNY25KZS8iLCJtYWMiOiI5YTNhNDE0ZDhmMjRiMDgxYzdhMzc0NjMzM2RkNTJlZTk5M2NlMDEzNjA4Y2QxYjllZDNlOTVkYzA4MTk4YmIxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:25:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNOcTRmT3dyVWJHQmFYMjgydG45QUE9PSIsInZhbHVlIjoiaUZUTzVLZ1JHOFZjMGxrZzFzSXFUZnZoeFJVM3hCTitSaS9rQmdvcWpiWUsyaml2a3hmd05XTWNOa1hmN2x5aVVtZXhibUdLZjZMSTRvTFh6MVV4OWlhRFJoeUxXSTRNZzhMRUhHeitRRFNQWThIeWtUUHNMWUl6TWZzMTRidkJKZHBFU01sRCtocW9Ec2JIUGdGeWhOS3NMRGJ5TTFUaFEwMFNyTlVDUFRQQlRRb3I5cXN2dVJsOHZwbjlQWU1Pd0RuK3V2NmZvU2tzVVd5c3FIQW5rSWlNZ3hKeXdlajZWMFJkejBSeDNnN1BvOW5kczhzd3U5NXpNNXFRZlhwQ2VCMUNXWGpXeXpVMkZDVzBCQWlZbVZ0aDJhNXFoVWN4RC9QOUpaMDl1THlTMzlkRWcwTWRaeE1jWFJuMDR1S1VrWjNyb0JCNDVXcjRpMGFsSkUrNktkZk5RTmR0OTlRNE9pbFM2WkxTTHFuSXJzdE5TamxOdk5NSll0NzVvNnFrb2dWT1Y3QnJZQjdaWk53eHE5MFJkdXUwa0NZUVhUK0ZybVlXeGozNW9VbmZiaUVmTUh6ay9kRG5PMXFSQlZiR3NKcENjWHA3ZHd4TkRLTWZXdVZWVmFuVFV2eXhBVzZRN1JUM0tTem1EUEJFbExXOEx5aW14ZFErSTZVZlArbEYiLCJtYWMiOiJkYzM2Y2RiYTYxNDAyYjI3ZGI2ZGNmOTNiNDZkMGNlODNjYjQ0N2FhZjA4OTM0ZWZjYjA1ZGRlODZlNTk5NmUxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:25:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1157645555\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1563707746 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>8</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tea-&#1588;&#1575;&#1607;&#1610;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>8</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1563707746\", {\"maxDepth\":0})</script>\n"}}