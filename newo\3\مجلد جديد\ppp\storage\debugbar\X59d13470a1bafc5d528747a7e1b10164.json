{"__meta": {"id": "X59d13470a1bafc5d528747a7e1b10164", "datetime": "2025-06-17 15:32:01", "utime": **********.283158, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174319.844948, "end": **********.283203, "duration": 1.4382548332214355, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1750174319.844948, "relative_start": 0, "end": **********.087514, "relative_end": **********.087514, "duration": 1.2425658702850342, "duration_str": "1.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.087535, "relative_start": 1.2425868511199951, "end": **********.283209, "relative_end": 6.198883056640625e-06, "duration": 0.19567418098449707, "duration_str": "196ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46092952, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02076, "accumulated_duration_str": "20.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1855478, "duration": 0.01781, "duration_str": "17.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.79}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2311869, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.79, "width_percent": 6.069}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2539492, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.859, "width_percent": 8.141}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1159276654 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173975623%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlkT0N1OTRIbm13bUNYczhpa0xJcGc9PSIsInZhbHVlIjoiWnNCb3ZJdVdacVprbmJ3cDg3eVRyYWZIM3Y2ZVNYUzM0NjBUanJzTkJDc25jTXZUS3hsbnphYWFvVWxMdHRteHlTTVFLRS96cXVUYnVOMVpUeGNWMkNOQ0krNDZrdExtUWUzZ1F5ejdLb1E2citDMkMyYko0bnVNU1hPdjNRZkpQWkN6SHRRVUViSjZudzVhODE4U3hGMHIyaXpGdkxlWjVHbkdyOXJodS9tVTErNTI4UzlqVXlGLzhvVkNlN0NHTjNMRC9jQVNobHpkN012enVzQk5yQmNWeXJUUk5IcEdWLzJUb2ZPcXhqQmR0a3NidWtUOGtteXI5dEgxTDNYUHhZVGI3b1p1TURqc2JwZjA5Y0lhWS9uUEs5dTVsVUcxaVRUbHNJTzRjS29yZHNacE9OYkFSNWRqTWtaemNqSVcvSmwvdGtMdHBGVzdPQ3UyZE9TR0FyWFphakgwaXVqaWcyOXMyeEVrSXRiRTJMSTFuRHQ2bDBxdEhxQWhXWFE2VFdxRUw2eHRQSGNKeGJJbmlzbGlxalkwNVovT3k4RytxYWQzNys0dnNzQ040SFozUEFQNjE3dkRvR085ZWJ4eWtCSmtiNUJFQmZqcTJuUTJ0Y012VXk3MDI0MkozejBmVElIVjdDb1BNNWF6RkQ4TTFHU3V3MDV6QjUwc1RjRCsiLCJtYWMiOiJjMGZiNWU2NWYwY2QzYmNkNmNkMjcyOWNiODAzZDk1Yjk0OGZlNTdhYWFlM2IyN2FmZWFlNzgxMGEyZTgzNzQ3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InBDTXJTcGhTZUppWGlGdCswMHZvdFE9PSIsInZhbHVlIjoiRkQrVmV2MlZ5NlBITG9JdDJqVGxRM2ZpRnNIb2lUa2lJRWkzOUlJNTQxSVJsb0Jkd3UxTkMyb2IydUN1Yms5VjNRV3U1ZWlDR3dmaEdqa0U5QUJLM3pZdHhyV0Y3Z3lHd1ZHcGZpNFVVS2QvdGJnMWp3dURtUUNEQVF5dTZ6YUJqa3ZHRnFGV1JidHR3WTFkS0MyQ2xObEI0cHVoVWkrcUorNlVRZlg1ZUk1K3NiU2hMYXB0ZFUyeVpBZVp4Q0hPMktVZHhVT3ZRRENBdGhLTlBPdVo2ZE0yUnMrbWJkRXdrOU5JUUNqSGwvNi9yRWtZdFRaRG5rM2ZvUGtRL283NUU2d1RmK01vMkxzYmdvQjBDaUdERjgxQXgxNHcvQngwVzhIM3ViRVlORTVpMjVQL3N4Wm93eFFBbXVCcGdBL2pkb3RLbWE3cjlFRW1GREcyMUFyTkVZRlhpN0kvMzF2ejJ0WUJ1S2dMMjQ2Um1pVzBoT090Si9mWE1VYjVBV1YyemFSdWhjY0RrcWxtUDdLWFdJNW1xYkJKMEZKOTArbHpKRlJicWhCakJsQXB5czBOV2Rjc0tjVHlpbWdIUVBOa2RJZml2SVJiVHM1bWZVSEtpcFQxaTNPclNXa3hJWllKdVZQd2ZlbFBpb2pscmlTOENuaXpPazIyOGtsZnc4RDgiLCJtYWMiOiIwMmFhMjdiZDg0ZjE1NTkwNTA5M2E2ZDQ1ODhlYWE1ZTc2NmJlNzkxYzNiMjJlMWJmM2YxYzA5ZGQ2NWNlODBmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1159276654\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1572811432 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1572811432\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1044496402 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:32:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZCcUNzNVdDaGxIcVZjQ1RkaSt4Tnc9PSIsInZhbHVlIjoicnByU3dNSnZjYzFmM0JiQmhCbFVhbFM4eU1MZFBoYVJOZFNrRXNoSnBYQ2VkS3VUZ0RzdFBxbFY0MThtRDNtYVM3VnVMd0VVaTN0b1ZRTmhJMjNGa2Q2VGpHV1VpaEx4VTVUV0twWHJ0UFA2Y1pNc2doenhaMmx6ZCtVRjNscWZUSThkdFRkUUpBSlkrMFVKcHg2LzZ5Mmg4d3UyaCtPVDNYR2Uybm96UXBIK0RrdFFGQ3dlNWJMcGlBbEpZNlBtajZWcU5tdFQvaTJiR3B4SnlyZ1VnUFBFNFNLN1NpQll5aTl6eTUyNnJIc2hoQlliRUo2dmkyQjBSb2cxOTBaM0NNMHk3bU43TjJ1bWJyZ3lVR1RTQ1dIUzF5U2xDN0pkY29hRXIzbUdUd2RKMHQvRldsSDRYM1lmU0JtS0JBcCtjQTM0c2k0YldYVCsvNS95Y3JRVXRyTDM3cmRxK2Y3K0oyUzc0RkVRTEhTenFhbkk0ank1N29BRlcxcUJRVWgwY0MrcG9HbXE1Ly9PQVcyYjJpSmdacWw0VXN2ZGc4bWhyZlRUc0RmVzQ2NVZxNXdLbm1zU1RDb2p6TmpseTZNbUtDbFVJZEErcU9Fa1RGY3F3Y0dKYmhNMWZtRHpPV3NXNzBuL2ZOMkovVG5jbS9Fc2Zkc1BEaUN1L1FwdjhFbE4iLCJtYWMiOiI3MmUwMTRiMTE4NmM4Y2QwNjdiYzY1YzNiN2Q1Y2FmYzBhMzI3ODViYzdiYTM0ZWYyNWU0NDcyZGZlM2I2NTkwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:32:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IitFVWlFYXZDU05Rb01xZDA0YkRoa0E9PSIsInZhbHVlIjoieHZ2eDAzN0ttMzZxekR6QnVqYWZHUlYvVGE0RU9yaWp0NFg4Y3g0OXZiVXJEbm5obWx5dUx5aTBrNXJlcWFUVEJsNUMyV0F5M2xSUXpqRjV4V0lpaStyUU1mUHhlUVF2QUt4NlZGa2hnVHMwWkovcXpkaGtEOGl4dFk1cnQ0OHBONEwxeUp3elJhWmdBNXBhWWNQTE9YckxidkcySUFJdmRjRFZRd28zd24vaXFWbTQvb3plYzZsMDU1a3ZrbG9ab3NjWDZBTWJJN2JlUERoMW5LVVRaL2ZRalNTQ3pvQWVHUHY3d2hLcy9ldHdiTHhodjU5SjFWY1I0Z3JwZ3ZlVE1lVkQxcFBmV2pQVjNrTmRyYy9LMm1BeHV5eXZWdFZmRHV5QlFSVnVMZTVKWElhb3IvVlJKby8xaXRpb0Y3Qjg0VWhZQ0NvQXJlckhKTFM4MjhNL0daUGtXY3lBcVh6angydE5Kd0xORmozYXAwcUxkT3lwS2JTZXZ0TWlTM05QMm5vdkVlUmFJYXpyekNRb3pINUUxTmpmSHlaSjI2ajA2S1YzUU9UZnQ0Vm5QREJwWTBxV3Vyem56d2FOZUJXd2oxSGxjQXo4Y1lCWStIYkxyYkxab2xnSUlvcTgvcXdWNVQvR0d1V1pXN2FCTU9WcnF3ZTgvMWZHbHc3UzdEKzUiLCJtYWMiOiJjZGQ5MjcyNDA5ZTQzNThjMmNkNzkxYzFjOWNlYTI2OGZhMjMzNWFjNTcyYWFjMzE4Mzg4MGVjZmE2ZDgwYzRlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:32:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZCcUNzNVdDaGxIcVZjQ1RkaSt4Tnc9PSIsInZhbHVlIjoicnByU3dNSnZjYzFmM0JiQmhCbFVhbFM4eU1MZFBoYVJOZFNrRXNoSnBYQ2VkS3VUZ0RzdFBxbFY0MThtRDNtYVM3VnVMd0VVaTN0b1ZRTmhJMjNGa2Q2VGpHV1VpaEx4VTVUV0twWHJ0UFA2Y1pNc2doenhaMmx6ZCtVRjNscWZUSThkdFRkUUpBSlkrMFVKcHg2LzZ5Mmg4d3UyaCtPVDNYR2Uybm96UXBIK0RrdFFGQ3dlNWJMcGlBbEpZNlBtajZWcU5tdFQvaTJiR3B4SnlyZ1VnUFBFNFNLN1NpQll5aTl6eTUyNnJIc2hoQlliRUo2dmkyQjBSb2cxOTBaM0NNMHk3bU43TjJ1bWJyZ3lVR1RTQ1dIUzF5U2xDN0pkY29hRXIzbUdUd2RKMHQvRldsSDRYM1lmU0JtS0JBcCtjQTM0c2k0YldYVCsvNS95Y3JRVXRyTDM3cmRxK2Y3K0oyUzc0RkVRTEhTenFhbkk0ank1N29BRlcxcUJRVWgwY0MrcG9HbXE1Ly9PQVcyYjJpSmdacWw0VXN2ZGc4bWhyZlRUc0RmVzQ2NVZxNXdLbm1zU1RDb2p6TmpseTZNbUtDbFVJZEErcU9Fa1RGY3F3Y0dKYmhNMWZtRHpPV3NXNzBuL2ZOMkovVG5jbS9Fc2Zkc1BEaUN1L1FwdjhFbE4iLCJtYWMiOiI3MmUwMTRiMTE4NmM4Y2QwNjdiYzY1YzNiN2Q1Y2FmYzBhMzI3ODViYzdiYTM0ZWYyNWU0NDcyZGZlM2I2NTkwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:32:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IitFVWlFYXZDU05Rb01xZDA0YkRoa0E9PSIsInZhbHVlIjoieHZ2eDAzN0ttMzZxekR6QnVqYWZHUlYvVGE0RU9yaWp0NFg4Y3g0OXZiVXJEbm5obWx5dUx5aTBrNXJlcWFUVEJsNUMyV0F5M2xSUXpqRjV4V0lpaStyUU1mUHhlUVF2QUt4NlZGa2hnVHMwWkovcXpkaGtEOGl4dFk1cnQ0OHBONEwxeUp3elJhWmdBNXBhWWNQTE9YckxidkcySUFJdmRjRFZRd28zd24vaXFWbTQvb3plYzZsMDU1a3ZrbG9ab3NjWDZBTWJJN2JlUERoMW5LVVRaL2ZRalNTQ3pvQWVHUHY3d2hLcy9ldHdiTHhodjU5SjFWY1I0Z3JwZ3ZlVE1lVkQxcFBmV2pQVjNrTmRyYy9LMm1BeHV5eXZWdFZmRHV5QlFSVnVMZTVKWElhb3IvVlJKby8xaXRpb0Y3Qjg0VWhZQ0NvQXJlckhKTFM4MjhNL0daUGtXY3lBcVh6angydE5Kd0xORmozYXAwcUxkT3lwS2JTZXZ0TWlTM05QMm5vdkVlUmFJYXpyekNRb3pINUUxTmpmSHlaSjI2ajA2S1YzUU9UZnQ0Vm5QREJwWTBxV3Vyem56d2FOZUJXd2oxSGxjQXo4Y1lCWStIYkxyYkxab2xnSUlvcTgvcXdWNVQvR0d1V1pXN2FCTU9WcnF3ZTgvMWZHbHc3UzdEKzUiLCJtYWMiOiJjZGQ5MjcyNDA5ZTQzNThjMmNkNzkxYzFjOWNlYTI2OGZhMjMzNWFjNTcyYWFjMzE4Mzg4MGVjZmE2ZDgwYzRlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:32:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1044496402\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}