{"__meta": {"id": "Xb34edcd441cdac7e651809ef34642284", "datetime": "2025-06-17 15:16:04", "utime": **********.817091, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173363.365537, "end": **********.817132, "duration": 1.4515950679779053, "duration_str": "1.45s", "measures": [{"label": "Booting", "start": 1750173363.365537, "relative_start": 0, "end": **********.605743, "relative_end": **********.605743, "duration": 1.240206003189087, "duration_str": "1.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.605768, "relative_start": 1.2402310371398926, "end": **********.817136, "relative_end": 4.0531158447265625e-06, "duration": 0.21136808395385742, "duration_str": "211ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46443216, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026779999999999998, "accumulated_duration_str": "26.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.713476, "duration": 0.023379999999999998, "duration_str": "23.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.304}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7729921, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.304, "width_percent": 5.601}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.78795, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 92.905, "width_percent": 7.095}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxkVURUY3JRUmhYVGZscDNxWVBaZEE9PSIsInZhbHVlIjoiQVhBQ1pYWFp2SXhOei9sVXllUmgvWGwwbVlLUnNhWi83d1FJM2E1QzJlWFhYajh6S1puTGpxQUo3cHBPWE5UVFhZdXVrVjlkRlFyVHJicnhtZmE0NmljaStaUmI3R2d1UlFwYkxJTEsvcVRiUGdHcTNWMm1SYmV0cWNKa1BEVTZlOHBsTUQ1K2N3eWRUNHNUOVNlcE5saXhhUTJjTlh0QVBxVVJuYXFRQVhIbCtEWHQ0bDFtM2hSamlNY0pxVzJra0N6UHl2dmcvZ3g0MnBZeENZOThIQ0tudGFSZlIvYzNsWituMUxwR01DZmJVU3F1YXF1S0JQZHJmQzVxQmc4V2k5V3dzNTMvTERIODcxRE10VDkzWmx3OUxHMGhCNVd1VHVoc1VaYkZPTlFFaVREVFZzamVXNzlJYTlNYytZWU1nbWpnRndFQXZKYXBVK0hnR28zK0ZBVURkWUFieUNUeUFMNGhkcFpXR29yRGg5MDZWdFB0K0FGQk5GUEhjT3VBN1pnTkRZY1JkdE9nVEJsL2x6aTZqeURPOXp0NURCYTdodU5YaDRDZnBoR0R3a2duL2RrSHpwV09KWS94VFNRSnZpZ0VyWmFORWRaQjZXSlQzQ1RwaUtKR0pKcHFEV3BGTDcvdVhLQ1NqL0t5YkoybFVqaTVoVEQ5MURCbm5VazciLCJtYWMiOiIwMmU0MGYwYmQ3NTkzNDY2ZGViYWMyY2ZlMGVjNDU0MDVkYTgwNTkwOTM1ODZjNmFmYjRhZmNhYzFhMGE3OWJjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkFYTlFUYW5SR2pva2xqbkVjS1E1WUE9PSIsInZhbHVlIjoiVHcxZ0gvSk5sbDlLck4xZnJWRGphOHduUy83WFlQZmJhaTdWM0Z1UVh1a0VtMnVlWDU4Qk1rKy9FZTdabFVzRUdGYTBQQ0RmaDVzMlZiSWJBTGZBRTZYbTM5WVF0dmxmWkd0c3h2dzFDMStlWjRJOTJ4RUpWS1J2TktHdzRkNk5laUFXYURSbWRtL0h4bjNkVDJjbjJ2cVRzL0ovZUlrZS9RaVcwOGVxU1FwK0RST1NuM2RDRjFtdjRYMGo0T1pwazVncDFrZU03dVYwYVdRTWhBRkxKYTJpcGZrYm1FTTdNeDQvSSsvZ0pRZnhtT0Q5M3cwNFlkcHRxeDdkNWtuOVM2ZnZDTnExbjl5eFA4RkdHdmRDTk9wY2hGSmlhamtQZ25tZllFdDlOdFVSdFNuM3lqNFV3aER4ZExOeElWUXh1QVBPRWcra2RPMy96K1BuZnloWkxYeWdIVHBSR1dKV1BFYWVnRnR6NEpvK0xid0NiZEVMT1N3bXBQamw5aVRTc25KY1A4RWhkT0N2SHZuQkNnVExVNVRHRmZkeVNzY28vNHNjUWdyYWM2VFl6NWRIQmlGVGI3dkVKc1RxZDJMcXF6aURtNGRSVjBSaEJzN09aU1J0aFJnQ3JZUnNzZFk0ZHgyVmZCcW9WK0hwaVFPZUlETTFweUR2UG1HMG53Yk8iLCJtYWMiOiI4MTQ0ZWFmYWE5M2QzMmU0NmMxOTY4MmI4NGZjMGYxYWEwOGVlMTk4NDE0YzRiMzY3MDUyNDlmMmYyMzBlZjQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-927984076 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-927984076\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-496653823 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:16:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhLWEM5ME03emlOemdxY3I3aUwyV1E9PSIsInZhbHVlIjoiMDhFRmZLYXRZS3BVK1dFNU9mQ2tSYVY3R1IwRFZsekM2WUhoN0pHSDRVYTN5aS9hWTNLK3ZTZjc0WGhNc0l3eUNhSkJIS3g1dTBGc0ZTN21QeWI0dzJKdlNoVm9teURlQytLdDg0ZWZsU3FlMjNEWXhwYTYyaStwVWN6YkV2Yi9oVzg2d2t1dUxDMVJFenZpamV6ZWhLVFc4SlZiY2UwWE9YUi9zNUZhODZFTW9ZclZhSG0vTUFjRC9Qa2xVR014V2ZqaG5zVVpLcnE4WVV3QjVpUFN2NzVnK0N6QTFYOFdSLzV4Vjl3NnNKTFBVbzFUbUFlY3Z0dVoyUmJ0dU9waktiMVl2SnU3WDdGQ0p6aVZueW5uUFFiaVYrczdsdmZpTWQ0SGFJWGdtS3NocmxpSWREWUJiMnBkYmRCZm8zY1ZYRG5QUVNNNWsxSU45aHhLQUFKQzd5d1czR0x2dmdiT2ZwdVUyRm9OTzdRWm5sVHR1b0RUdUJjaWFCamphMmlveGF6MmtXdHdqTFpQekpVVDZuZ1g1S2xsei9Wd1BzZFlkMlFUSWNwK3ZzcTJpaDNyYS9KM3pic1hnZGlienRUeHJXTGQxOUVjNGpkLzZJdjNybzdTcHFOZFljOVdHQjRhcGxJV0FSZHp3amlkbGgwdXRQenZMYXRoeXRPNml5ek4iLCJtYWMiOiI1ZDcxZWI0ZTc2NWUxYmE4ZjI0ZTQ1ZDBiYmJkMWI0NDljNWRhYTI2MjQwNGI3M2QyZTcyZWMwNDE3YTc0YmUyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:16:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im9oRWRqdnpoaFdIbUk2SkVoYzdTQ3c9PSIsInZhbHVlIjoiNjI4azNXdWVVVFgwVzJ1MTJNbXFYOXZsWEtleW1Va3I5K0ZQVHRoZ1pLY0xteXhoUXVadGkrYWl1YzltVU9iS00rQmY2QjV3a3Fyd3NYWjVZTXlpcXFYazB3ZnQxbWNDMi91ckNkL3d6MmUyOUIzU0FVbjI3cG0wUVFEcnBEcW5DTWUvcmxKSU1UYmt6OG9yUWpTTEZxNk40Q1RaelRZa1ZFVUo2RDJyZnU5eTlxTWw0bDU5Sk54VXBOZVlZTmhjVzdld1RDMnRnMGRsMFRQdFVMNDM0czdsNzByK3RPbDQ0RDVSMkdYU2JYbzBjWGNUMlZyZjMzNGVCSzZUTkU0Qk11SE5iM0loakFxSWNTanVEQU83cW1lTzJXYUtSSjR4TU55RGF2OExPOWt0NUt5NnZ3OGhLNkNaNGVSMTRPTTg1NXhYZnlqaTNqdmJyd09US3ZKRU1hdGhjUWgwZEl6YWdvell2M2xOdjlZNTEvRDBZZkREV0FuekdiOVppTWZqM0llQWZJUzlRZ21FbkU5dGJxZG93QURIT1oxUGtwQnliUjAvT1pGZSttSngvVWJzaERuVCsxdmxJcEZFZlhGWHFsQkt2eHovV3RLb04yek43QXpMK3RHU3dpaHI0WkttVytlbmZCTVVhZmM3NjdkUVRKVzU2YXJ0MkpuSk9lanIiLCJtYWMiOiJiYzY0NzYxM2E1Y2M1YmNmZTUwYTRlNzg5M2IwNzAyZDI5MWNkYWMzM2RmNzhhYzI5MjRhNTRkZDU3YzQxNzY0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:16:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhLWEM5ME03emlOemdxY3I3aUwyV1E9PSIsInZhbHVlIjoiMDhFRmZLYXRZS3BVK1dFNU9mQ2tSYVY3R1IwRFZsekM2WUhoN0pHSDRVYTN5aS9hWTNLK3ZTZjc0WGhNc0l3eUNhSkJIS3g1dTBGc0ZTN21QeWI0dzJKdlNoVm9teURlQytLdDg0ZWZsU3FlMjNEWXhwYTYyaStwVWN6YkV2Yi9oVzg2d2t1dUxDMVJFenZpamV6ZWhLVFc4SlZiY2UwWE9YUi9zNUZhODZFTW9ZclZhSG0vTUFjRC9Qa2xVR014V2ZqaG5zVVpLcnE4WVV3QjVpUFN2NzVnK0N6QTFYOFdSLzV4Vjl3NnNKTFBVbzFUbUFlY3Z0dVoyUmJ0dU9waktiMVl2SnU3WDdGQ0p6aVZueW5uUFFiaVYrczdsdmZpTWQ0SGFJWGdtS3NocmxpSWREWUJiMnBkYmRCZm8zY1ZYRG5QUVNNNWsxSU45aHhLQUFKQzd5d1czR0x2dmdiT2ZwdVUyRm9OTzdRWm5sVHR1b0RUdUJjaWFCamphMmlveGF6MmtXdHdqTFpQekpVVDZuZ1g1S2xsei9Wd1BzZFlkMlFUSWNwK3ZzcTJpaDNyYS9KM3pic1hnZGlienRUeHJXTGQxOUVjNGpkLzZJdjNybzdTcHFOZFljOVdHQjRhcGxJV0FSZHp3amlkbGgwdXRQenZMYXRoeXRPNml5ek4iLCJtYWMiOiI1ZDcxZWI0ZTc2NWUxYmE4ZjI0ZTQ1ZDBiYmJkMWI0NDljNWRhYTI2MjQwNGI3M2QyZTcyZWMwNDE3YTc0YmUyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:16:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im9oRWRqdnpoaFdIbUk2SkVoYzdTQ3c9PSIsInZhbHVlIjoiNjI4azNXdWVVVFgwVzJ1MTJNbXFYOXZsWEtleW1Va3I5K0ZQVHRoZ1pLY0xteXhoUXVadGkrYWl1YzltVU9iS00rQmY2QjV3a3Fyd3NYWjVZTXlpcXFYazB3ZnQxbWNDMi91ckNkL3d6MmUyOUIzU0FVbjI3cG0wUVFEcnBEcW5DTWUvcmxKSU1UYmt6OG9yUWpTTEZxNk40Q1RaelRZa1ZFVUo2RDJyZnU5eTlxTWw0bDU5Sk54VXBOZVlZTmhjVzdld1RDMnRnMGRsMFRQdFVMNDM0czdsNzByK3RPbDQ0RDVSMkdYU2JYbzBjWGNUMlZyZjMzNGVCSzZUTkU0Qk11SE5iM0loakFxSWNTanVEQU83cW1lTzJXYUtSSjR4TU55RGF2OExPOWt0NUt5NnZ3OGhLNkNaNGVSMTRPTTg1NXhYZnlqaTNqdmJyd09US3ZKRU1hdGhjUWgwZEl6YWdvell2M2xOdjlZNTEvRDBZZkREV0FuekdiOVppTWZqM0llQWZJUzlRZ21FbkU5dGJxZG93QURIT1oxUGtwQnliUjAvT1pGZSttSngvVWJzaERuVCsxdmxJcEZFZlhGWHFsQkt2eHovV3RLb04yek43QXpMK3RHU3dpaHI0WkttVytlbmZCTVVhZmM3NjdkUVRKVzU2YXJ0MkpuSk9lanIiLCJtYWMiOiJiYzY0NzYxM2E1Y2M1YmNmZTUwYTRlNzg5M2IwNzAyZDI5MWNkYWMzM2RmNzhhYzI5MjRhNTRkZDU3YzQxNzY0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:16:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-496653823\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}