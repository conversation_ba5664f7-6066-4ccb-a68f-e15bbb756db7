{"__meta": {"id": "X0e11ab021ebc8ffbe796b0346c3053cf", "datetime": "2025-06-17 15:19:12", "utime": **********.827457, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173551.218822, "end": **********.827492, "duration": 1.6086699962615967, "duration_str": "1.61s", "measures": [{"label": "Booting", "start": 1750173551.218822, "relative_start": 0, "end": **********.616321, "relative_end": **********.616321, "duration": 1.3974990844726562, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.616343, "relative_start": 1.3975210189819336, "end": **********.827495, "relative_end": 3.0994415283203125e-06, "duration": 0.2111520767211914, "duration_str": "211ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46093752, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024480000000000002, "accumulated_duration_str": "24.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.72476, "duration": 0.0216, "duration_str": "21.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.235}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.781875, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.235, "width_percent": 6.781}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.804293, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.016, "width_percent": 4.984}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6ImY0NWFJUy9KVVB0ZGc2WG1pcVBnYUE9PSIsInZhbHVlIjoiTTJ0U1lualJ1bkJRU1RvSlQ0ZU1Edz09IiwibWFjIjoiMjAzNzNmZjMwN2JjMzE5OWVjZWZlYjUzNmM5ZjA5ZjY0YTRiOTdlZGUwYzQwNmRhZDIxMWM0ZGQyYzlmMzkxOSIsInRhZyI6IiJ9\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1458447654 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1458447654\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1292507592 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1292507592\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1796755007 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1796755007\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-3977500 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImY0NWFJUy9KVVB0ZGc2WG1pcVBnYUE9PSIsInZhbHVlIjoiTTJ0U1lualJ1bkJRU1RvSlQ0ZU1Edz09IiwibWFjIjoiMjAzNzNmZjMwN2JjMzE5OWVjZWZlYjUzNmM5ZjA5ZjY0YTRiOTdlZGUwYzQwNmRhZDIxMWM0ZGQyYzlmMzkxOSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173379875%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im1uNmg1K0UxSEY4SW9uTWhkcWM2WUE9PSIsInZhbHVlIjoiczc4S1FnenkwZ2dtbG1QSjg0RHlvc1hWZENlNFc4RmpzdnpzUExEcW42NWxyM21na3phNnlYRnk1UFdKTGJTVTdRUHNsTlRzMlNabWR0b1FhWmlDSEpjNHIvRWdsUjMrQkpZUkZqYk94Q0FlTW5UOVBzMmllOUhlbkpzTDgvVndScjY5SkprM2drTjgvZHE5ZHlhWmp4cVp3T09yclNjNTluT2dJVFJHZzgzMmM5aUxxbmU5Z01jWE13ZEloMnJOUktvam9JYjkwZmlaN1oyR2hHcWgrdXlCZkJQeDVmbjdHYU5aQkpGSDJyVDFmcHQrb1AxT25uZ24wYmh4RE5OQ2JNTTNNNlkvT3h0R2tpaGQ5QmRQd1RoWjdZM1RYUWF0LzZYKzlmc05DQkhjMDFXTGczd21JZGFIVFcrbGEyd09rM1NoTHVQeHpQYi9LTGlod2x6ek1nQThiU2lmS1pTdDNHenBIYkh0ZGw4TDZmeXFQRGRnZUJVUlc2M25WbXRjUEQ3czhDbmV0bkV4L0FNL1lrYk9OQ0Z2L0pHb1dqbkQ5bEFUQ01mdUh1K2p4RWp2U3M0VFgveGk1cEtSZFBzZXVzbk1IaEQ5R0t3SHRNbWw4ZVFzOTkybTZuQWFKaE1Ma1R2OHUyT0UvMDVkTmlLUzJoS3U5b2VWazRNYUFKcW4iLCJtYWMiOiJkZTJhNmRiZWM0NzkwNmU0MGZiZjA1ZDQwNWUzMmQ5NDg5Y2FiNjYyOGQxNjcwNzI0ZWFkOWQxZGVjMGQxYTU0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJIUDQ0b0V2VkdhRzhQZ1ZIdmxPQUE9PSIsInZhbHVlIjoiVUorTVRwa0RuUXBKYmFJL1FWVFhvZDRJZHVzanRLa0w0Q2ZFQlcrdGN1SEUvVVZTQlZXWUFtYTFZK0NubzBncHJoYU9PbnkxZ1BYL1h2VWkvSFlrM0ROU1ZPRTFBaHlIay9wKzdWTmtrcjdoTE9pS1BEZllEM25VY3REa242bTJYbmdRY3RZSWhNTUNid3pLR2FTNFc4TjRzNkdUNUhaL0lTV2MyY1BUdEFqRXcwT2pSZllNd0h2MWNxOVJ1OHlMYWlDbS82dmVPa2h3Z21ybTh3b1NjVURsYVZDcWlhTGZyUnE2L1ZRR2pxTEVDSHRvL2s4RlJtUFRlMjBVU0hmdXZFUURBY2orZnlLa0VZeWpmZ2w4bS9FRDlpSHh3VGwyV0N5dEN4Ri9yTlpMZUhLaDdSWmFpSWFyeHk1bXVmRFEydUFDem1CTkZWKzE3RFlJYTUvdEdvTlFXTVh4aUtSN0trcVpkODRrMlFjVGc4bVVIb3pmWXFRMXRKN3lBSkZERCtVd3lJc1BybEE5eEx4eFNUZllHR1hTcGU1dmVxL1pqWnhsV01lL1FLc2NLTDc5cVdLWGdmMHNzVDAwVWlPMFljYW1kd2hOYzRsTDFFV1kxdS96blRQYis3aHdFdU45ZWhKTG9raVRnd0M2QUFqaVRCQk1jdkM0RmJ2cHhBMVciLCJtYWMiOiI1MTNmZmQ0Mzg3OGJkNTBkMTU5MzcxZDI4MDYyNTU5MmIxZTBhYmE2MWM3YzcxMjE3NmI5MTc3YzczMjE3YzcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-3977500\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-736676002 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-736676002\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-149305705 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:19:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inh1bVZ1dHFBRlB1Wjk1L3JFT2JwQmc9PSIsInZhbHVlIjoibGZubFRmNEdxUjUxSUJPbFdDYkhlbEJGMnFCeFVWL3lsUksyQjI0WXVHZmdUWnRuR2piTTlrQ1Y3M0J0MnN5YnRGWmo0Nk1EZWVJWWNhdlJpREFPMVllNzRTVlYrSEpUbjVkMmdtZ29reWJPaEYyc000Z1Z4QmdacHRkcHVMV2s4OTBmeW9BRzgrdVpneUZiOCtFR24vSFdSR0tEOFU5TEdNSkt3Q3lQaGc5aEhZRVhFdWtUc1lnWlBJbStzSWVEQ3V6ZGtuSnBOUC9CT1BIQ3hwV29YSzRocHAvR0dhTlo1dWkvWFBzaCtYaFltNjdZZ1RZaWk3bUo3K0tHa0dxTVRSZUQ1TlFjZWRFQ041OFJvOFR0d3dQeFlORkRHL2tWZVRFdkp0d2FRaGVDVVlnVnNvMnBucUJBN1RRVU9zci93N2wwb0Zyc2ZraWRaMG5mZytvQ3BpaXFuNmgrL2lUSk0zbDIrQjg0WFFOT3lKV1JQNzZwb0lUOUtaT3lLaG1IN1BhVlgyWTlvakJLMWRuZjlGRDNqaUMvTG1jNUJwZTFnVWRYWk1Zajhxa3YxazNSM2wyRXNNUGUzSWM5eFluRmY0ekpVNENDMmRJdHYwSTFHQ0djYW1qZnRUZC9rL0IzNk5mS3ZGbHpIWlppcGNUMVUxOVI2Uld3WDl2VnZnU1giLCJtYWMiOiJlNjJlYTY3NWJmM2ViYTBhZmYzY2Q2NDJhOGY5OTk1NjVjZDBmMGZiMDM2NjJkY2E2YzgzOTRhMzQxZTQxNWZhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:19:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkVTYW5SQ25kaUc2b1k4V1dmT0xrSHc9PSIsInZhbHVlIjoicThaaXlFWXg1cU5uYWpycEVCcmtiRDJTajR3UkRNVGxFL0lEZnFuQWJTeG13MjAzNE9ob2M4Mi9tZTFFOVl5Tk83UjdHVG1UcitkaDJJUXF5NnF2MEtGWCtYMGcxM2diSWZGVFRaWjBGbXprZml4MkNFS3h0K3RGODBrVms0Rm1YNjc1MU1HMzFXaHJMOFpEcWtUdkRMQkduczVqUU9kdmNiU1I1Sk1sMVh0ZFJKRTdiYVpYd01iOHA4YzR6Yk5jR1RtcDZIQm9WRmpPRUd3WlI5WmZpMnN6TjNyVG9JMmxuWHRITlEzVGFyaXVrQXBCeitOT1VkZVQxVWFlOGFZYnMrTUZqcnlLMnZrSC9CY1AvcVhsT3J5elpscXR2dm56Z1JBeGFvM2xjbHBRMDh0VWJ3alZTWGdLcllnWnpFRk50RHlHMEJwL29iT0ZMQ3pLN05lUEZFcFdiWEtIc1JKbGlzc05vcUdxV1phcGhWakZSNC9xS2pmd1JaSjgwU2kyc1p6UGZZbkJJbk0xcGxKbS9yaWdUd3hNNWVJT1h5dTAvYm9uTU8wbk9wbUYxbUFQZitpNnhRSG9xVERhYjNtUUYzc2hLUG9kSkFCenJQTUQvaloveVpMSThhK3p0NXdDaXFRTGdacGxpQkcyV3FKVXZsaEpSN3ZDZmJhNWU4Mk4iLCJtYWMiOiJhYzQ3NWYyYzNjMDdhYTIyMTIwMTljYmIzZDMxZjg4Y2MyYjBjNTVhOTQ5ZTJiNWQyNDg1MjdmMTIyODRhMTg2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:19:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inh1bVZ1dHFBRlB1Wjk1L3JFT2JwQmc9PSIsInZhbHVlIjoibGZubFRmNEdxUjUxSUJPbFdDYkhlbEJGMnFCeFVWL3lsUksyQjI0WXVHZmdUWnRuR2piTTlrQ1Y3M0J0MnN5YnRGWmo0Nk1EZWVJWWNhdlJpREFPMVllNzRTVlYrSEpUbjVkMmdtZ29reWJPaEYyc000Z1Z4QmdacHRkcHVMV2s4OTBmeW9BRzgrdVpneUZiOCtFR24vSFdSR0tEOFU5TEdNSkt3Q3lQaGc5aEhZRVhFdWtUc1lnWlBJbStzSWVEQ3V6ZGtuSnBOUC9CT1BIQ3hwV29YSzRocHAvR0dhTlo1dWkvWFBzaCtYaFltNjdZZ1RZaWk3bUo3K0tHa0dxTVRSZUQ1TlFjZWRFQ041OFJvOFR0d3dQeFlORkRHL2tWZVRFdkp0d2FRaGVDVVlnVnNvMnBucUJBN1RRVU9zci93N2wwb0Zyc2ZraWRaMG5mZytvQ3BpaXFuNmgrL2lUSk0zbDIrQjg0WFFOT3lKV1JQNzZwb0lUOUtaT3lLaG1IN1BhVlgyWTlvakJLMWRuZjlGRDNqaUMvTG1jNUJwZTFnVWRYWk1Zajhxa3YxazNSM2wyRXNNUGUzSWM5eFluRmY0ekpVNENDMmRJdHYwSTFHQ0djYW1qZnRUZC9rL0IzNk5mS3ZGbHpIWlppcGNUMVUxOVI2Uld3WDl2VnZnU1giLCJtYWMiOiJlNjJlYTY3NWJmM2ViYTBhZmYzY2Q2NDJhOGY5OTk1NjVjZDBmMGZiMDM2NjJkY2E2YzgzOTRhMzQxZTQxNWZhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:19:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkVTYW5SQ25kaUc2b1k4V1dmT0xrSHc9PSIsInZhbHVlIjoicThaaXlFWXg1cU5uYWpycEVCcmtiRDJTajR3UkRNVGxFL0lEZnFuQWJTeG13MjAzNE9ob2M4Mi9tZTFFOVl5Tk83UjdHVG1UcitkaDJJUXF5NnF2MEtGWCtYMGcxM2diSWZGVFRaWjBGbXprZml4MkNFS3h0K3RGODBrVms0Rm1YNjc1MU1HMzFXaHJMOFpEcWtUdkRMQkduczVqUU9kdmNiU1I1Sk1sMVh0ZFJKRTdiYVpYd01iOHA4YzR6Yk5jR1RtcDZIQm9WRmpPRUd3WlI5WmZpMnN6TjNyVG9JMmxuWHRITlEzVGFyaXVrQXBCeitOT1VkZVQxVWFlOGFZYnMrTUZqcnlLMnZrSC9CY1AvcVhsT3J5elpscXR2dm56Z1JBeGFvM2xjbHBRMDh0VWJ3alZTWGdLcllnWnpFRk50RHlHMEJwL29iT0ZMQ3pLN05lUEZFcFdiWEtIc1JKbGlzc05vcUdxV1phcGhWakZSNC9xS2pmd1JaSjgwU2kyc1p6UGZZbkJJbk0xcGxKbS9yaWdUd3hNNWVJT1h5dTAvYm9uTU8wbk9wbUYxbUFQZitpNnhRSG9xVERhYjNtUUYzc2hLUG9kSkFCenJQTUQvaloveVpMSThhK3p0NXdDaXFRTGdacGxpQkcyV3FKVXZsaEpSN3ZDZmJhNWU4Mk4iLCJtYWMiOiJhYzQ3NWYyYzNjMDdhYTIyMTIwMTljYmIzZDMxZjg4Y2MyYjBjNTVhOTQ5ZTJiNWQyNDg1MjdmMTIyODRhMTg2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:19:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-149305705\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1585550378 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImY0NWFJUy9KVVB0ZGc2WG1pcVBnYUE9PSIsInZhbHVlIjoiTTJ0U1lualJ1bkJRU1RvSlQ0ZU1Edz09IiwibWFjIjoiMjAzNzNmZjMwN2JjMzE5OWVjZWZlYjUzNmM5ZjA5ZjY0YTRiOTdlZGUwYzQwNmRhZDIxMWM0ZGQyYzlmMzkxOSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1585550378\", {\"maxDepth\":0})</script>\n"}}