{"__meta": {"id": "X6d17e2d25a042c79ac9ad850682591de", "datetime": "2025-06-17 15:26:16", "utime": **********.716783, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173975.304873, "end": **********.716829, "duration": 1.4119560718536377, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1750173975.304873, "relative_start": 0, "end": **********.522255, "relative_end": **********.522255, "duration": 1.2173819541931152, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.522279, "relative_start": 1.2174060344696045, "end": **********.716834, "relative_end": 5.0067901611328125e-06, "duration": 0.19455504417419434, "duration_str": "195ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46106264, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02641, "accumulated_duration_str": "26.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.615191, "duration": 0.02283, "duration_str": "22.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.445}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.66663, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.445, "width_percent": 4.847}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.687565, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.291, "width_percent": 8.709}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-990003765 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-990003765\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1287271135 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1287271135\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-124542601 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-124542601\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1637530660 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173970357%7C6%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVoN2JQTUp2T1RPL0F2VHp4cGphVnc9PSIsInZhbHVlIjoiS0pRSDFNdEk2WFc1dkFIMExocTN0TXp2VTBnK3BoM0ZTYWttWEFobUR3bTNpTnphN2FlRUh6TE9SWDJzRlA4a25kN1grZ3psVFZsV3ozQjM0QmEyQk5hdVViMlUwamkzamVlYS9GMEJwZEFkOFU1dnJpdDJqSm5sWFFnNkhMQ2dWZXVxM2xuaHpFNDBFTk1XaVVFMjAvWEZ4NFl0TzNZUnpnc2VDcWZTZ1dJeGZXc2M2azhlMXRsVUkzcHMxNEhUZkdVQkY4RW9FL0p3c0RIcUg1YXd5S0Q3bS91aVY2R3l5ZzU0aU13TnYveGx6NUY0ZXdWSzQ5Y2NUZUwwdzNQRGN3VjVyVU0waWdSdk5Qc2VHT3ZqUzhyTTY4SE0vd2ZOSmh4cW5veVdkMVpuVjFTZmhQcmxJdjNpbVVnblJYUEoyd2hOTzBuNUhzNEJmZXRSZjhCWjdwRnFXU3RGSFk2eDZWNHhTQlhOUXk0c1VNMHZhWWVSRStab3NKSmtXV29seGVkQzZxd09ta0hYVkgzU3Qxam9PaUk3V0YwejlPTVErc3U2ak1wcjdMYjVmU1gxL1FCYnVlMlUvVFduVkVMZGl1djVqa2luWXJrZGx4VkR2QWN3N0kyVlNlTU10YzJvTWJVSVFHQWlyQzJ3Y0hySy9jNnJuRE9kSGt3dHV6NmUiLCJtYWMiOiJiMWNlZDQ2YTMyNjNjYWRkNWRlNGFlYzkxOWY0YWRhZWEzMzU4ZWNjMDdjMzdiZTJlMWQ5ZjQzMDUzYjY5M2IxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InlCOUZYMnJXY24vbUVHUUJnUUt3cUE9PSIsInZhbHVlIjoiN2xOQ21KR1draU5aRVl5K1JiNkphdkt1ck5QdWk0MFpmQWVYeXZNQ0tQMmRpTnZYSGRkd1YrWDhsMHNjdVZ4STNEL1NXUDVJV0tMb0pZZU1kU2VNMkVpZlV2K0NNZG4xemtYT1NmbkcwM00rL0ZiSTBMY3p5UlQxbzgwNkxkWGhJNHNEM3hsMXhLT3o5akk1dEc3azR6cXZJV1RGN0E2enJUdWY4YUpQRGUrbk5SNGdoN0xtL0QySWJNanZ0amNtZHNKR29ONkRuZHRVSHI4OVVGOVM3Z29GL1VQMHkzek9MOEkxOG5BMmNSMzNaVHRxdklXOVBheW8rMFREL3MrdnNGMy91ODlCOFBqNXlmbzRoMkp4S1AwK0psM1pZcGY4amJTSW8xQmNEeXRSN0FLWXpiNEs1MnRaeFcxWS9GK0ZtamsxYUZUa2RZV1pFU0dWdG00WnNSM2NPcW9JMENWd0duVVo5OWlUL0hNZ1c3Z29vK0ZBRC9obXYxUlZHVm1TdG1SeEx5TVMxZTdQczZSaGhBaWtWeElHcTlMVFpTL080emtRVk9ycDRKR2RPaTFrVHlYQkZ1ak0wK3NJWmFwSFB2VnNVUFNFRXVxWjZnRitGZlpnTG4rNEx3cXRuREhSbFJrZWk5YjM2RXRuRmNUa0hGWC9QcDhkS2s3S2FLMGQiLCJtYWMiOiI2Zjc1ODZjM2MxOGY5Njg4N2NiMGE1OTc1MDM5MGE3ZDU3N2Q1MGVjN2RlYjZiZjMyMGM1ZWMxY2I0NmEzOGFmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1637530660\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-181635034 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-181635034\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-243640381 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:26:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhUSENJbVpHaDdHcCtQQy9mY014WHc9PSIsInZhbHVlIjoid0dqRnFKdmVPNzl1SFFGc1ZadWc1VHlwR3c1d0dWOXUwc1RJTnBtQkVSVEt5bU5WNEIrVHhmbG95bFo5TG9FT1lrdENweWVZWXlSTWF6eVRBVVhuanRzZGlWOFNNU092cUN3UFF2cnpPditBUEhhWHA0OWppVFpqYjJUUXhpdmR3MCtHbkVMeW1oYlcxMStiQzV6Q0dNZzlyTVVVQ1JrVGFBajlJaVRoNDcrb2pjcXY2OFkwUFZiYXpmbzFFM1drcG5KOEltcVQ3My90c3NKcWV3clZrclQ1dlNnVU9oNlRjL25aQjg4Y2FwZ0tSYnVMSDYrZzhyV3pnZHBLYVZpNXJzVzZrakUxQUMvTlpmZklrSFlkUkNCR2UzSnJnTkgwQWhONUxIU05wQlBZT290S1d0ZWc3R1NGL1R5WmhkSE90VUlXMHcvNmQ4MXp3bmdyWW1uaHlKODNURHh0d1NqM0cvc0hQaUhUZXZOOHJ3Qm1kSjdURjZCTzVXVUQyaE8wa1JYMnhUNWlwUXJ3M3FnNzl5VTZXOVpqMFRRNjRnTlJCRi9hQzJRS1JYR1NHOVJDVkZUZzdLcGFybm1Lcm1MQVhocWtDa2tSRjUvR1FFQ1RrRmtxR0cybmZPdnNZS1VkWkxPS0lkSEp6TFFmSGw3Y3F4Z1RkMVNYZjQwSkJ0TW0iLCJtYWMiOiIzZDJhY2Y5Mjc1ZGQ5ODI1YjMzYzgyOWY3Y2Y1NTVkMzkwNzZhZDUxOTMwNTI3NjY4YWFiNmI1NjVkYTA2ZTg1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:26:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IktqN3RlU213bVdoZnd2T21RcU82OUE9PSIsInZhbHVlIjoiaFh5Tmk3OG4zSFVWOFF5K3ZpYmp1a1BhTE53WWRyOVY5d1BPTXRHRWtwTVJwT0xRaElXcjZ0K1pxdmNIVnhRb3V5VUlWZmNhOUlPN0JYSXQ4WDNWTFM3THZBenl2TTNaS09RUFI4R3dRLzRFd0MzdUZJRDZzZ2F2cFptYWp5ZWFtWE5kOE8zb0xYUUdLZEpMOWE3bmdtL0lsb2ZtUDcrY0ZrMGtZY2xOZUlSM0phSjhJOTN6SzBYN1JYeGtmM1drakRvOXJwWkZBejRQblZSQU5ENlUzVGlaVXRpdDAvN2EwM1EwVzF3ZzhJdVJSZWZqSDBtWWxRUmtUUVc3Y3lUN0IzT3UwaWFXVndndWRzQ3p0VEZPRUJqZUUzZ1RrcFNMcXhEWVR5OFBnbkdycWdXWWs0U1lsczNRcHp2aXk1d1YwcEVVYTBJUFh6QkZ1YnExK09qc1VqbjFrYVczL2xqelVSemlKQ2VZR2tyZXN3QmFlT3JLMHVSZVJkYmwva3J2SUxRdDhPaU1rMmJkd2F3dzlnYmdYaVNQcHlrbXZWZiswR2hMQ2lzZ2R3VFBjRzEyejR5L1UyYVB6RXpxYll4ZzBFbmR6MDhhTU1aMzlMcjE4a2JaMHV5ZTl2VEordEhVZ0R1SnJ5TjBJdWEydXhSYVkyMzJ1NnpyanRTV0RWbk0iLCJtYWMiOiJlYzkxNGQzNDRkMDE0OTAyZTVjMGUyNWEwOGNhYTYxZDI1Yzc3NTVlYWM2ZGNmYWVjMmYzNmI2ZDcwYzc0ZDE1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:26:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhUSENJbVpHaDdHcCtQQy9mY014WHc9PSIsInZhbHVlIjoid0dqRnFKdmVPNzl1SFFGc1ZadWc1VHlwR3c1d0dWOXUwc1RJTnBtQkVSVEt5bU5WNEIrVHhmbG95bFo5TG9FT1lrdENweWVZWXlSTWF6eVRBVVhuanRzZGlWOFNNU092cUN3UFF2cnpPditBUEhhWHA0OWppVFpqYjJUUXhpdmR3MCtHbkVMeW1oYlcxMStiQzV6Q0dNZzlyTVVVQ1JrVGFBajlJaVRoNDcrb2pjcXY2OFkwUFZiYXpmbzFFM1drcG5KOEltcVQ3My90c3NKcWV3clZrclQ1dlNnVU9oNlRjL25aQjg4Y2FwZ0tSYnVMSDYrZzhyV3pnZHBLYVZpNXJzVzZrakUxQUMvTlpmZklrSFlkUkNCR2UzSnJnTkgwQWhONUxIU05wQlBZT290S1d0ZWc3R1NGL1R5WmhkSE90VUlXMHcvNmQ4MXp3bmdyWW1uaHlKODNURHh0d1NqM0cvc0hQaUhUZXZOOHJ3Qm1kSjdURjZCTzVXVUQyaE8wa1JYMnhUNWlwUXJ3M3FnNzl5VTZXOVpqMFRRNjRnTlJCRi9hQzJRS1JYR1NHOVJDVkZUZzdLcGFybm1Lcm1MQVhocWtDa2tSRjUvR1FFQ1RrRmtxR0cybmZPdnNZS1VkWkxPS0lkSEp6TFFmSGw3Y3F4Z1RkMVNYZjQwSkJ0TW0iLCJtYWMiOiIzZDJhY2Y5Mjc1ZGQ5ODI1YjMzYzgyOWY3Y2Y1NTVkMzkwNzZhZDUxOTMwNTI3NjY4YWFiNmI1NjVkYTA2ZTg1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:26:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IktqN3RlU213bVdoZnd2T21RcU82OUE9PSIsInZhbHVlIjoiaFh5Tmk3OG4zSFVWOFF5K3ZpYmp1a1BhTE53WWRyOVY5d1BPTXRHRWtwTVJwT0xRaElXcjZ0K1pxdmNIVnhRb3V5VUlWZmNhOUlPN0JYSXQ4WDNWTFM3THZBenl2TTNaS09RUFI4R3dRLzRFd0MzdUZJRDZzZ2F2cFptYWp5ZWFtWE5kOE8zb0xYUUdLZEpMOWE3bmdtL0lsb2ZtUDcrY0ZrMGtZY2xOZUlSM0phSjhJOTN6SzBYN1JYeGtmM1drakRvOXJwWkZBejRQblZSQU5ENlUzVGlaVXRpdDAvN2EwM1EwVzF3ZzhJdVJSZWZqSDBtWWxRUmtUUVc3Y3lUN0IzT3UwaWFXVndndWRzQ3p0VEZPRUJqZUUzZ1RrcFNMcXhEWVR5OFBnbkdycWdXWWs0U1lsczNRcHp2aXk1d1YwcEVVYTBJUFh6QkZ1YnExK09qc1VqbjFrYVczL2xqelVSemlKQ2VZR2tyZXN3QmFlT3JLMHVSZVJkYmwva3J2SUxRdDhPaU1rMmJkd2F3dzlnYmdYaVNQcHlrbXZWZiswR2hMQ2lzZ2R3VFBjRzEyejR5L1UyYVB6RXpxYll4ZzBFbmR6MDhhTU1aMzlMcjE4a2JaMHV5ZTl2VEordEhVZ0R1SnJ5TjBJdWEydXhSYVkyMzJ1NnpyanRTV0RWbk0iLCJtYWMiOiJlYzkxNGQzNDRkMDE0OTAyZTVjMGUyNWEwOGNhYTYxZDI1Yzc3NTVlYWM2ZGNmYWVjMmYzNmI2ZDcwYzc0ZDE1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:26:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-243640381\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-706696585 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-706696585\", {\"maxDepth\":0})</script>\n"}}