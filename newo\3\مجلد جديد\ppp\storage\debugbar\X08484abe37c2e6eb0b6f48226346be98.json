{"__meta": {"id": "X08484abe37c2e6eb0b6f48226346be98", "datetime": "2025-06-17 15:31:32", "utime": **********.383653, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174290.664357, "end": **********.383699, "duration": 1.7193419933319092, "duration_str": "1.72s", "measures": [{"label": "Booting", "start": 1750174290.664357, "relative_start": 0, "end": **********.179317, "relative_end": **********.179317, "duration": 1.5149600505828857, "duration_str": "1.51s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.179349, "relative_start": 1.5149919986724854, "end": **********.383704, "relative_end": 5.0067901611328125e-06, "duration": 0.20435500144958496, "duration_str": "204ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46346064, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1696\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1696-1706</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00855, "accumulated_duration_str": "8.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3093002, "duration": 0.0073, "duration_str": "7.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.38}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.353561, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.38, "width_percent": 14.62}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1877828906 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1877828906\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1345271472 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1345271472\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1921336648 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921336648\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1161573459 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173975623%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldVMFJnYytJVEFyVVBSMjFsYThUWlE9PSIsInZhbHVlIjoiblpHM2hwRjhxdDd6VFFKOG9JanBjSXZja1pudFE3VEgxRjZyMXBUTWFsOWVjcWkyM1RqVkc1Y2luMTUxQ2lIcTBNY21CeUViNXVsNU1Zc1kxZGZMTnJWcmNTcC9rVjlwMUkyZTdxQkdFbUZ2SzlWcW1DSFNhaUwrVUFtVXBoaG5VUGZ5cWkrWmdzVDgweXdyazg1aTdZWnNWZ20xcHBqTVRPZTJoOVZhUEZPdVN5ZGpmTjdlVUw3VW9CNzdKUy8ya0hySmpwVnYxRFg4VGd0NzRURkJJaUxrNW5FZnZHTnFvdnpUdERoSS9ZRXpjcCtKSHQyeXFMT2dmRml3bEtMUmVLNDNLZjlkczNYZmplTXA5bUZnYStMRmE1alh0aUlzMmh0ZUEwcmdYeWdDbUZmVVZ6UW9xd1VWV2pBUnZBaTBvdzczUk1naVR3d2lzZUZTRGI1ekI1MXdTTGpqYWovdGFTWXBXbnNGU21qT3EwOEwxaFRsSmRCTW5zQnNMRjdsN3dPNVViQVNsYXk1NHBMR2VHbllrcnpwRzFrbkozVC9VN0ZUV3A0SHlvak5VN1U4Q1MvQnZLRHlKZW9Bb3MxZEliREc3RFBIK1FLODA3VlduazJvczNON2o3ekF3NUVKL3MyK2FqU3A1V1IrbzZQdXdBeE9RTHBWWDF0Uk0vWm8iLCJtYWMiOiIwOGZmMmM0Yjc5OTA1MjM3NWU4Y2U1ODQzNjNkYzk3NjE1MDhiOTE0MWE0NTkyM2RiMjU5NTMyYmM0YjU5YmU5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InBzODl3TVJZVUZGZUZYc0ZEWGVtV3c9PSIsInZhbHVlIjoiU2ZYaEJoTlFlUnMzSEZiZU4vNjJ3bHZjWTN5b2ZrVXgydnZxVmJhMytvMnUwU1NKdGdOdERUbzhxZVlDcDJjNmJYVDZFRUVhdFJ1emtYbGJPNUYvb1NoOWw3L3ZXOGtSQlRRL0JkaEFWVUc5c0VoZkpnTXJSREdxTjNiNmNxck5GamlhTy9ScjNicUVFeXNCVE5KTU5NUUNEMWl3S0JneDBBSlBQd3ZvdnYrejE4UGRaR2JZZnJNRHM1M3R4MnpRdWo0cUU3eS9LR3VMdHlEQTZxLzhhdkRHc0xNdS9ZbDIrbEdvMkF3eUN2K1Q1b1h5MjRLa0JHZ0dTSGxHY0dmeUl3d0lpaXM0amhTZ2lTZTFKNGFBMXBoQUpITVdydVZGMmxKdzVaUGVaOG02NVFtVGg2NXVjTVV0OGxRR1NSekowQUx4R0xMR2YwTkt6V0ZYclgxSUN3VDBLSzNVVUhiY2tjSDVwb01GYXZ4WmVUdXhlZ0RCVk1CS1RwVURUaUdzTGtRRW1VWUNvUFZYZ09zOWVyMEhib3ZoaEgzcXU2eXo2Y0JvN0lpLzcxWG5BV2xvMTFaYmJoMDkxVWJBN0paMlZQWk93a3ZrV1Z2S2xzR2RnTWM3SGNjZmtuU0hQM0U3QVZBNEMycXNqZVRrenpHRG9SWUFncWtIUmkrVEtvVzQiLCJtYWMiOiI3ZTM4NWE4YWE1NWI5ODA3ZmUyOTAwNDJhMzg1MzlhYThmMDIyNTVkOTRjMDAxOWE5ODI4MDlkZGY3NWQzODE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1161573459\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-416419019 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-416419019\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1906510980 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:31:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imo1anZKWXJ3WmR1ZWZkWUk2ZnR5VEE9PSIsInZhbHVlIjoiK0t3bzN6VktFQXJQZkNZYWV6MHhSTDBZbWFWOS81WVFJNStONWk0cDhyaTYra1kzZVJMTWdEZ2l1a2k4cU54ckNEb0k2NGhTMjdQM04zTHYreGpKUG9pdGNUYllzZkJoNjFGeHNJNnVUTXlJNVlYOVZOdElkdHlTNm92KzNaOTZlSE5BcjE1RTFiR2ViSmEzM0tseVNSZmloZStmdWJ3bWY3OFhWakpNdWhlWGU4UDQydUZVUnBXeFBoS08za1JGZ1lobWdmdXNDRjlRZzIrcm91elRBcXdzWFQrL3J6V0grZ2tyM0lWajVwRkxzME5vN25WY2VJTkFnYXlkVWVSVk92U0dwRTRKVkpJYVZkbmEzZlJidEY2V3plMzY1Rkd6YWY5R050WHUxOVZjdHEwMGVPSVN1RnUyUnFLcXFxNjY5c2JrZ3NmYlAvQjF2Um8xc3FObzJOa29rUVRrSEQySGQwMzNxSFpUVlJ5TGhwTlg5RkVGOUdiRjk5TGxJUmJWYnJQMlVnVWFEazlLYnlsbmxkalBFYU0zWjM2anZwcDdncVFpN205aHgvS3hNenVvMDhGSkNuYmZNbzg4UW1NYzFQOHAvNExCaXNMN1RoSXh0OVppOEZvS0dyRmpOR0FJSlZQZUNJMVM5cGJQMis1dFdNcW53WmtqN2JlNmlSMXYiLCJtYWMiOiJlMDY0Mjk2NWViODU1MWQxY2VmNWFiMDlhMjNlOWNiODMxNmNhMjQ1MzBiYzEzMDM3N2ZkYTRmODYxYTAxN2U0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:31:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IitsSGt0L0F4MVJOd1VXeVBUdDlnQmc9PSIsInZhbHVlIjoic2lvS2U3bnNBV3FjWWZxRW0wNXBzMXhHVWNWb1B1Zk4zT2kxVTg4ZitRRVJGekJVK2o0RmlhZzdyV3NnbkZ5OHIzRWFneGZKcEhpRzRyMmVqSU5seUJoV2lFNmsxbXRxZlRrUzRobWRUNnA5MzdyQXlrZFJUTHh1TjBlRFhEeGFMWkx0UGRackFlSi9WbHB0Zm1sOFh3MEl3UythZzR3ZnNFK2dhNjVWVW1sRTZUL2ZiRzdrQWdnQkJtUG9sa3VMSWRyOWlSNkt2U0ZsUFNLWlJYN0tuVHIrdUt5aWdqdmZMZEhvdWZUNllmVjBUTnpYR1J5RVpnaUJYRnNoNHlFQkk1TklhbjY5VDVvb2VNWGFuYUViK3dMVThxbGNpam1zZk13ZDd4S2psTXpIb1dMRTVpL3ovcFF4TjRzVlJLTEFtRTBrcjN1Ym1XS1RRcGplZWx5ckN1T0FZR3oyeEI4eVVZK20rNXNmUHFGZEczQU4vbDFEdmRqYUU4V0FoeXQ5T2FLWG9BbXlEYStLWHFJN2NJdWcrWE9MVjBmVlNPTVdoR2EvK1ljTkFLUlc5S3o3WDdQN2FrUm9sZk5uZWR1WVFTUTQxMU85cmFBMHNDUndGZU9ZbkVBSlFteExoUWFwVFoxbkJIYzZiYSs2a1hncENBTHBYS3lEUnp5NmlPVysiLCJtYWMiOiI5M2NlNmU1ODA1ZWNjODJmYzgzOWFhMmRjMDg2NzEzYWFhZmFmMDg2MmM0Mzg5NTdhODg5MWNjZTEwZDQxZjE0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:31:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imo1anZKWXJ3WmR1ZWZkWUk2ZnR5VEE9PSIsInZhbHVlIjoiK0t3bzN6VktFQXJQZkNZYWV6MHhSTDBZbWFWOS81WVFJNStONWk0cDhyaTYra1kzZVJMTWdEZ2l1a2k4cU54ckNEb0k2NGhTMjdQM04zTHYreGpKUG9pdGNUYllzZkJoNjFGeHNJNnVUTXlJNVlYOVZOdElkdHlTNm92KzNaOTZlSE5BcjE1RTFiR2ViSmEzM0tseVNSZmloZStmdWJ3bWY3OFhWakpNdWhlWGU4UDQydUZVUnBXeFBoS08za1JGZ1lobWdmdXNDRjlRZzIrcm91elRBcXdzWFQrL3J6V0grZ2tyM0lWajVwRkxzME5vN25WY2VJTkFnYXlkVWVSVk92U0dwRTRKVkpJYVZkbmEzZlJidEY2V3plMzY1Rkd6YWY5R050WHUxOVZjdHEwMGVPSVN1RnUyUnFLcXFxNjY5c2JrZ3NmYlAvQjF2Um8xc3FObzJOa29rUVRrSEQySGQwMzNxSFpUVlJ5TGhwTlg5RkVGOUdiRjk5TGxJUmJWYnJQMlVnVWFEazlLYnlsbmxkalBFYU0zWjM2anZwcDdncVFpN205aHgvS3hNenVvMDhGSkNuYmZNbzg4UW1NYzFQOHAvNExCaXNMN1RoSXh0OVppOEZvS0dyRmpOR0FJSlZQZUNJMVM5cGJQMis1dFdNcW53WmtqN2JlNmlSMXYiLCJtYWMiOiJlMDY0Mjk2NWViODU1MWQxY2VmNWFiMDlhMjNlOWNiODMxNmNhMjQ1MzBiYzEzMDM3N2ZkYTRmODYxYTAxN2U0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:31:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IitsSGt0L0F4MVJOd1VXeVBUdDlnQmc9PSIsInZhbHVlIjoic2lvS2U3bnNBV3FjWWZxRW0wNXBzMXhHVWNWb1B1Zk4zT2kxVTg4ZitRRVJGekJVK2o0RmlhZzdyV3NnbkZ5OHIzRWFneGZKcEhpRzRyMmVqSU5seUJoV2lFNmsxbXRxZlRrUzRobWRUNnA5MzdyQXlrZFJUTHh1TjBlRFhEeGFMWkx0UGRackFlSi9WbHB0Zm1sOFh3MEl3UythZzR3ZnNFK2dhNjVWVW1sRTZUL2ZiRzdrQWdnQkJtUG9sa3VMSWRyOWlSNkt2U0ZsUFNLWlJYN0tuVHIrdUt5aWdqdmZMZEhvdWZUNllmVjBUTnpYR1J5RVpnaUJYRnNoNHlFQkk1TklhbjY5VDVvb2VNWGFuYUViK3dMVThxbGNpam1zZk13ZDd4S2psTXpIb1dMRTVpL3ovcFF4TjRzVlJLTEFtRTBrcjN1Ym1XS1RRcGplZWx5ckN1T0FZR3oyeEI4eVVZK20rNXNmUHFGZEczQU4vbDFEdmRqYUU4V0FoeXQ5T2FLWG9BbXlEYStLWHFJN2NJdWcrWE9MVjBmVlNPTVdoR2EvK1ljTkFLUlc5S3o3WDdQN2FrUm9sZk5uZWR1WVFTUTQxMU85cmFBMHNDUndGZU9ZbkVBSlFteExoUWFwVFoxbkJIYzZiYSs2a1hncENBTHBYS3lEUnp5NmlPVysiLCJtYWMiOiI5M2NlNmU1ODA1ZWNjODJmYzgzOWFhMmRjMDg2NzEzYWFhZmFmMDg2MmM0Mzg5NTdhODg5MWNjZTEwZDQxZjE0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:31:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1906510980\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-659280340 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659280340\", {\"maxDepth\":0})</script>\n"}}