{"__meta": {"id": "X4cd9f261aa85462d5503357435190525", "datetime": "2025-06-17 15:31:30", "utime": **********.932678, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174289.354424, "end": **********.932714, "duration": 1.5782899856567383, "duration_str": "1.58s", "measures": [{"label": "Booting", "start": 1750174289.354424, "relative_start": 0, "end": **********.642453, "relative_end": **********.642453, "duration": 1.2880289554595947, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.642476, "relative_start": 1.2880520820617676, "end": **********.932718, "relative_end": 4.0531158447265625e-06, "duration": 0.29024195671081543, "duration_str": "290ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49205912, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01751, "accumulated_duration_str": "17.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.767306, "duration": 0.00649, "duration_str": "6.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 37.065}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.805958, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 37.065, "width_percent": 6.625}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.861175, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 43.689, "width_percent": 8.567}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.869723, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 52.256, "width_percent": 7.824}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8885639, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 60.08, "width_percent": 22.33}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.901503, "duration": 0.0030800000000000003, "duration_str": "3.08ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 82.41, "width_percent": 17.59}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1025818744 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1025818744\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.884999, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1791658007 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1791658007\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1542837054 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1542837054\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-918061599 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-918061599\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1897792313 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173975623%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldVMFJnYytJVEFyVVBSMjFsYThUWlE9PSIsInZhbHVlIjoiblpHM2hwRjhxdDd6VFFKOG9JanBjSXZja1pudFE3VEgxRjZyMXBUTWFsOWVjcWkyM1RqVkc1Y2luMTUxQ2lIcTBNY21CeUViNXVsNU1Zc1kxZGZMTnJWcmNTcC9rVjlwMUkyZTdxQkdFbUZ2SzlWcW1DSFNhaUwrVUFtVXBoaG5VUGZ5cWkrWmdzVDgweXdyazg1aTdZWnNWZ20xcHBqTVRPZTJoOVZhUEZPdVN5ZGpmTjdlVUw3VW9CNzdKUy8ya0hySmpwVnYxRFg4VGd0NzRURkJJaUxrNW5FZnZHTnFvdnpUdERoSS9ZRXpjcCtKSHQyeXFMT2dmRml3bEtMUmVLNDNLZjlkczNYZmplTXA5bUZnYStMRmE1alh0aUlzMmh0ZUEwcmdYeWdDbUZmVVZ6UW9xd1VWV2pBUnZBaTBvdzczUk1naVR3d2lzZUZTRGI1ekI1MXdTTGpqYWovdGFTWXBXbnNGU21qT3EwOEwxaFRsSmRCTW5zQnNMRjdsN3dPNVViQVNsYXk1NHBMR2VHbllrcnpwRzFrbkozVC9VN0ZUV3A0SHlvak5VN1U4Q1MvQnZLRHlKZW9Bb3MxZEliREc3RFBIK1FLODA3VlduazJvczNON2o3ekF3NUVKL3MyK2FqU3A1V1IrbzZQdXdBeE9RTHBWWDF0Uk0vWm8iLCJtYWMiOiIwOGZmMmM0Yjc5OTA1MjM3NWU4Y2U1ODQzNjNkYzk3NjE1MDhiOTE0MWE0NTkyM2RiMjU5NTMyYmM0YjU5YmU5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InBzODl3TVJZVUZGZUZYc0ZEWGVtV3c9PSIsInZhbHVlIjoiU2ZYaEJoTlFlUnMzSEZiZU4vNjJ3bHZjWTN5b2ZrVXgydnZxVmJhMytvMnUwU1NKdGdOdERUbzhxZVlDcDJjNmJYVDZFRUVhdFJ1emtYbGJPNUYvb1NoOWw3L3ZXOGtSQlRRL0JkaEFWVUc5c0VoZkpnTXJSREdxTjNiNmNxck5GamlhTy9ScjNicUVFeXNCVE5KTU5NUUNEMWl3S0JneDBBSlBQd3ZvdnYrejE4UGRaR2JZZnJNRHM1M3R4MnpRdWo0cUU3eS9LR3VMdHlEQTZxLzhhdkRHc0xNdS9ZbDIrbEdvMkF3eUN2K1Q1b1h5MjRLa0JHZ0dTSGxHY0dmeUl3d0lpaXM0amhTZ2lTZTFKNGFBMXBoQUpITVdydVZGMmxKdzVaUGVaOG02NVFtVGg2NXVjTVV0OGxRR1NSekowQUx4R0xMR2YwTkt6V0ZYclgxSUN3VDBLSzNVVUhiY2tjSDVwb01GYXZ4WmVUdXhlZ0RCVk1CS1RwVURUaUdzTGtRRW1VWUNvUFZYZ09zOWVyMEhib3ZoaEgzcXU2eXo2Y0JvN0lpLzcxWG5BV2xvMTFaYmJoMDkxVWJBN0paMlZQWk93a3ZrV1Z2S2xzR2RnTWM3SGNjZmtuU0hQM0U3QVZBNEMycXNqZVRrenpHRG9SWUFncWtIUmkrVEtvVzQiLCJtYWMiOiI3ZTM4NWE4YWE1NWI5ODA3ZmUyOTAwNDJhMzg1MzlhYThmMDIyNTVkOTRjMDAxOWE5ODI4MDlkZGY3NWQzODE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1897792313\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1211601895 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1211601895\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1355713111 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:31:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpZOFNseGE2VTA2VnhiekN3bjF6cEE9PSIsInZhbHVlIjoiUm93czZEK3FVa0REaHlMQ1A3NDFOOXQ2aXMrZnVQNWlOTkpSNnhxK3JLcTRKS3IvcUwwRTM5RmhVanczalpEei9qc21GMXgveW90S1pKcXRoUmhvVndObkIwS3ZrVEp0LzQ1dzQxTGZ5SHZFYWxyWXErdHRvVk85UmJ3RVJXUjJjcjg0d09iWlBaYmpOd251cEdtdDA4cmh0NWtGMW1ieDlSNFF1ZHg5ajNjbVdvRFpVVjhRaCtvWkhFOEpRVEpEQytEVnpPSmJtem8rKy84MEhXUlVFRjkyVTlYdk5JTWZMNy9DRlBqNEdGVlpkbWFvUVBoeXN1UGVsZ2xyWWV2MWpRNUlqVkw2cnBtVFlzWkQ1M2RFb2hZK2xhUmVWTERuRm1KRDNRYXBucHhnai9SWS9HZ0hpQyt5VEJwSWpxUmhBV1FQQWNmd2VDRDhUcVJUVE14NmpodDF3dlZQZDJFTmIzZC81UGdvcWZsNjg3QTlqckZzWnFjd0JvVVAzSlpONnFseElta1hkWS9NVnVFWVYvbmViWkpDNlpGTUFWYmk2M2NuTWoxOEt6V0pPSUhPUGU4QWVwaHZsclBqMER3dTRzQWFwK0o2UGk4RkdRcFFhbFNKWTB1b3VjbCtBM0lDZnNvRWZHUFg0WEFLYUVkQnVMYmRrU05sVms3MVFKZnUiLCJtYWMiOiI5ZjM4OTMwMmE1OTcwODJlYmY1MTMxMjUyODFiODNkZTdjNGFlYTZkZjQxNGZjYTdmMWUyZTlmZDNiNDUwYmE3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:31:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlJvcDJsVmJoTURZb3dZTFdEOUMzZUE9PSIsInZhbHVlIjoiS3FnUWczS2RTWldPcTgwRWRnc3lqeUExK1BEaWJsUzJka1BIRG5nTUYxWW83Y0c3Q0NnVWZ1Q29tNVMxUlNTSkZLaEhZTkxROVRTQ1l5cHdnenZ5VFc1WlA1aTNaM0JnQUVvSXhKaXhVa1BTMHpZWE1xV2krRk8xZXNvV3VISmU0N1M4cDVjVW1hbTUyNVVsOXRob3p1YzFsUHdTb1BmWmh2Mjl6OUhiUUNKYUFEOGxIbzhkNjNsSXhpdEFqMzZTbDNCQmcwUW1tNXowRUs3bEl0R2FOTUYrVUJtOTRQRUJKbkxtSk1NeW5WempEQVNzcnFKMTIvMnR1VHgzMlg1Sjl0dDh1d3lWV013S093UUx2cE5LbEpYU0JlUVJ3Vis0MTY4Qnd4NUcyRk1XVmtXM3Bhb3RXNXlKbmtIZGI3cTlUQyt2azk1a001dGU1cmJSandZUXJuc1gwcGMzT1ZROEkyc2k0aDBKcFNrbUNjdmJYNVdJc25qV1RGSDVJejZQWC9INkZTdXNEU1BaYVdwd1hyTXlDRURKNllBWkVRV28wOHdLMjFpUWtOdGN2ZEFleTZuSS9iOE9ObHdUa2ZwRzdYbklRbmhHNThaSXh1Y1JXcnZyVC9pbExjVmVDZndDRDZPRmI2TkxnODhGckNCb0x6dmRINUEwU2lXS05HZzIiLCJtYWMiOiJkNjJhMGUxZDZkYTBkYzMwNjc1ZDc2MzE4NjVjMzAxYmRkNjRmYTNiYzBiZDE3M2M3YmY4ZDNjMjU5YTUzYjhiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:31:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpZOFNseGE2VTA2VnhiekN3bjF6cEE9PSIsInZhbHVlIjoiUm93czZEK3FVa0REaHlMQ1A3NDFOOXQ2aXMrZnVQNWlOTkpSNnhxK3JLcTRKS3IvcUwwRTM5RmhVanczalpEei9qc21GMXgveW90S1pKcXRoUmhvVndObkIwS3ZrVEp0LzQ1dzQxTGZ5SHZFYWxyWXErdHRvVk85UmJ3RVJXUjJjcjg0d09iWlBaYmpOd251cEdtdDA4cmh0NWtGMW1ieDlSNFF1ZHg5ajNjbVdvRFpVVjhRaCtvWkhFOEpRVEpEQytEVnpPSmJtem8rKy84MEhXUlVFRjkyVTlYdk5JTWZMNy9DRlBqNEdGVlpkbWFvUVBoeXN1UGVsZ2xyWWV2MWpRNUlqVkw2cnBtVFlzWkQ1M2RFb2hZK2xhUmVWTERuRm1KRDNRYXBucHhnai9SWS9HZ0hpQyt5VEJwSWpxUmhBV1FQQWNmd2VDRDhUcVJUVE14NmpodDF3dlZQZDJFTmIzZC81UGdvcWZsNjg3QTlqckZzWnFjd0JvVVAzSlpONnFseElta1hkWS9NVnVFWVYvbmViWkpDNlpGTUFWYmk2M2NuTWoxOEt6V0pPSUhPUGU4QWVwaHZsclBqMER3dTRzQWFwK0o2UGk4RkdRcFFhbFNKWTB1b3VjbCtBM0lDZnNvRWZHUFg0WEFLYUVkQnVMYmRrU05sVms3MVFKZnUiLCJtYWMiOiI5ZjM4OTMwMmE1OTcwODJlYmY1MTMxMjUyODFiODNkZTdjNGFlYTZkZjQxNGZjYTdmMWUyZTlmZDNiNDUwYmE3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:31:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlJvcDJsVmJoTURZb3dZTFdEOUMzZUE9PSIsInZhbHVlIjoiS3FnUWczS2RTWldPcTgwRWRnc3lqeUExK1BEaWJsUzJka1BIRG5nTUYxWW83Y0c3Q0NnVWZ1Q29tNVMxUlNTSkZLaEhZTkxROVRTQ1l5cHdnenZ5VFc1WlA1aTNaM0JnQUVvSXhKaXhVa1BTMHpZWE1xV2krRk8xZXNvV3VISmU0N1M4cDVjVW1hbTUyNVVsOXRob3p1YzFsUHdTb1BmWmh2Mjl6OUhiUUNKYUFEOGxIbzhkNjNsSXhpdEFqMzZTbDNCQmcwUW1tNXowRUs3bEl0R2FOTUYrVUJtOTRQRUJKbkxtSk1NeW5WempEQVNzcnFKMTIvMnR1VHgzMlg1Sjl0dDh1d3lWV013S093UUx2cE5LbEpYU0JlUVJ3Vis0MTY4Qnd4NUcyRk1XVmtXM3Bhb3RXNXlKbmtIZGI3cTlUQyt2azk1a001dGU1cmJSandZUXJuc1gwcGMzT1ZROEkyc2k0aDBKcFNrbUNjdmJYNVdJc25qV1RGSDVJejZQWC9INkZTdXNEU1BaYVdwd1hyTXlDRURKNllBWkVRV28wOHdLMjFpUWtOdGN2ZEFleTZuSS9iOE9ObHdUa2ZwRzdYbklRbmhHNThaSXh1Y1JXcnZyVC9pbExjVmVDZndDRDZPRmI2TkxnODhGckNCb0x6dmRINUEwU2lXS05HZzIiLCJtYWMiOiJkNjJhMGUxZDZkYTBkYzMwNjc1ZDc2MzE4NjVjMzAxYmRkNjRmYTNiYzBiZDE3M2M3YmY4ZDNjMjU5YTUzYjhiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:31:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1355713111\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-755270426 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755270426\", {\"maxDepth\":0})</script>\n"}}