{"__meta": {"id": "X1b7410c7b81bdfd66b5890eea6d2eb3a", "datetime": "2025-06-17 15:16:20", "utime": **********.961071, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173379.563665, "end": **********.961107, "duration": 1.397442102432251, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1750173379.563665, "relative_start": 0, "end": **********.784552, "relative_end": **********.784552, "duration": 1.2208871841430664, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.784572, "relative_start": 1.2209069728851318, "end": **********.961111, "relative_end": 4.0531158447265625e-06, "duration": 0.17653918266296387, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46105856, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021, "accumulated_duration_str": "21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.877568, "duration": 0.01781, "duration_str": "17.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.81}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.920617, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.81, "width_percent": 8}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9394772, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.81, "width_percent": 7.19}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1467894182 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1467894182\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-103820894 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-103820894\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1468623088 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1468623088\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173372152%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InNSanBuS29ZUWppc3pUVHY5bVFmd0E9PSIsInZhbHVlIjoiU3g4R0NNUm9sYjVaZDBHSjhvYXNBcWNheVJnNUY3YmQ0eGkwNlBaRC9FNmVqUWtKUDNha01kUTNPMzRzbG8rQlRwUDJCUUpVek1JcVI2ZXprZzBjb2QrMlhWY0xWdGZVZTM0aElHclh2Y3Iwa2F0Um13NVAxWElXZ0ROR0FXbS9QS005VHducldqUS8wY3c3TGt2aGwrV1J6bXVNQVk4Yk4xRmNZL2ljaGNYa3d5SURicFhaMktqYkhCVy94bTlSaDBwaGc0L0hsU2xwaFlYOGx6M1A5M2h0MlFndHo1dTJkUXNEOUwvTUIxQnhOVnpoUkEycER1emxvTVJjTDlXRUhhMUd6YlJDcHV1cE8wMXdIbDlSNXVTeStCMzdpVmVzeitvZUJ1TkxpOWsyL0YrVDRDMmtWaWd5N09qTHJhR2ExVS9BaGhSVW8vZVlVdi9mekVRblIyQzNzdWcrd2JKSVE3bWRyZ2pJRDBDN3ROUTh6YmgwZEJ3ZWo1UTNaVXpKMDYvYmlaWVBKSHJVNlRBWml2aU9sVUhqbDlLUDQ5WVlrdnN2eWhhaE0wZjBYbWc0MlRqdHV2YzVDQnhJdlRKcHlraHpnS1RNRjBPbURqdDBpdjFYL0dNb2Jqd1IzdGRwYjR0K3krRDVDZWdyMHBFVTFFd29SVGJrYkIxdHFIWWgiLCJtYWMiOiJmMzA0ZmQ5ZmExOTI1NmEzMWYwOGI5ZWI5NWQ5YmQ0N2I0ODYzZmNmNWUzMmU1NzRlZGY3ZWIwODNhZTZlMjk1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBaNjVWQ1lTemNvUm4vQjhSNXpSM1E9PSIsInZhbHVlIjoiZitnaHh4UWc3aitxckZnMjF0T3A1QXpwbWxDQWlCL2sxSEh1MjFiM2pRb0pnb2x3WWN1N2xMRllta1ZBSU53dzJIQXMvSFNLdFRaK3hFV2RmOE5NN1pCbXZZaU9oM2lmTmMzUUpPak9QQ0tJSEJsc3ZmYjBzTjZkcDJaR01mdEFoazdoVUpoWDN0ZHBFM0ZqYzJDZnRQSkpKeVhEcWFJRnFoNDVjVyt3WkVCQUpoWjMyb3I5UUJvdnp2VlBMZUMrR0pqYVBtN3hseVFkSUlKUXU3Qkhha3FrYUJsSi8zWUJOc2JXNG00OWZKUzZaTEhRVFZTYTlJQlZaUXh0R1c2ZGYxaFh6Qm5WYXF2Uk5wSXNEeTdQSVVLYXRNREhtbDlhUDVxN3hVZzJ0RlhVMjg3TW9LUjcwWGZVUVJlK0Z0YkxjdmY4dlRwaHNxb1NjV2JVN3Z5MUcwZlpLalcrelZQRTNjR0UvRWovL2xIVnBHSVlQVWo3MEswN1ZteUk2WVVYbE5UQ1lYMUJWZnZrU25HaE5WVnZsb1FtZkJpL0NPSTVoUEJmOWJ6Z0VkUGpjTDNiaXpNdjdIZHNFN2N2RGFiOUtxaUJoUEloSGplMGJFdkpBbTFLdnBCcE1tYUpNMGRjT2hzSlo1VEQyS1NxVzBuR0JzNEdjVWpRU0hWakp3RnkiLCJtYWMiOiJlNzFlYTQyNDdhNDcxNGYzZGU2MGQyMjdlN2Y2YmNlYjJjMjc5NTlkNDAzOTE2ZGU0N2FhMmE2ODE3NDY3NzU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1616585635 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:16:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNZZEJUR25aWUpBQjhDQmlYTC9LcXc9PSIsInZhbHVlIjoibi9SSmpmY3VUVDI2Um1MU1NiYVkxbTI4U3BYa0FCZ2JrMWxxcHFPckVlQVlyV2prT01Fa2luQnZKT00zaUNXbU95TGFRTjJlaFhNem16VDZtQTZrRHNNU1d4WnNYT0U0OWNYVnRxaGVnczMzVzZmQ1h0OVhER0g3SzV2N1l0a09Rcys2WS91SGlWZ2dKWDVaRDBSN29ZaXpvbEU1M1BnS0R3ejNXcXU0VjJqQ2lDNEJTY0tJUDFwMUdYWHpBL1JHMEE4dVlpSmRXOTJBRzN1N3lXWGlBc3NpbGRPZFZMZEh5SlYxZmN4aHdRUG1ZNGhjSnVhRzl2TDRNQldDWnBiQUFBcGxnUXo5YTNCeGhteHZpa3B4OThkN2RHdHV2OW1xTmhiOG4rcWVxVG4wRzZHZ2FnMWkwZTRYb25FcVlnQTBXTXQ1RWdPaisvWGlsMjZjWVhVRy85VjMvUlRNZGJXWkdOK3I2MFBvMk5aV1Zab0hmeG5hTjVxU1lDb083N0l1M0xsTkpraEx5U2NiSjl1RjdPbE9FQW9mSTFhaERHcHVhODYrY2prQUhHSjhTU0VqanljOEdzMHpmWkFiSXJPYU9pb1VRaEZ6dmt5Ym5wWDRubXRUd0ZMdmQzOWYyajJaUHJDVHlIYU9kVmpMUmFDMlpjZ0dOQjUzU1dMZ3FOOEoiLCJtYWMiOiIwNWNjZTRkYTdhZWUzNGMwOTliOTA1YjM2OGZlYWI0NDYwNmM1NWFhZDFjYjE4NjM4MDZhOThjMmVjMjI1NWZkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:16:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Iml1eVlUcTZhTXU0MVlpU3dFTnJrK2c9PSIsInZhbHVlIjoiSzZhNk1zMW5Cd1ZKd2NIWm5NR09vY0cyd2NvTEUrZ2xHS1NqNFNWMitCcVJ4UUswNHJseDhJVzAxL0owRlBPWDBaemhpOFpCdmFDckNBSzhYT0duNjI0Q0dBRjB2cjNXUC9RbkZxZ21OdXZDZENhbFZHODRnLzNxWG5vTjBOVFppNkVBaE5MRFErZ3Y1eUZSUnR4YXl5ZmM1cGZXcXN0bUZITG1jaWlHeS9kYm1YUXNteCtBNU9oSzJzYThwT3RZb0hBbzJUSmZmYkNlcStISjh1cEFKV3pvcnZmaVExUTZyem5tTWZMMkVzei9DOU1tMXRMSDVjSzR5eDd2d3Nrbkh2RmRLOFZPOUo5eU8zOGEzMjJWczdPMmE0dXFuK0ZoNk11NnlXTnMvbFBFS3NZZUE0amxvUFQzOVF0TXIzcFJLdE1JaWZhSk16SmdNWklTS1psMjQ4c2liYlRMNFBaVlhVekRycFloSkJESTVySE5tN1NFZWRja053RzlJbE4rM0hYY3UwQ0twdWgwbTh5S253aE9VdnJYZ1F5YmN5OW0rdUJnMFNHc0lYWnZKUDR3citUQ0VnVHZXTVY2UVFNcy9yM0tCRm15ZGZPanpXQUxBNnZpazB5UUhrYTRMdXpQOWVwemd2ZUs1V2NoYnlhaVZySmhGQnZJQ1pnSUwvR2ciLCJtYWMiOiI1MDgzNTZmMWIxYWVjMjA2MGRiMzFlZjY4ODkyNWIwMGYzMjY0NjM5Yzg3NGNkYmE4YTRjNjAzOTljOTFhYmU2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:16:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNZZEJUR25aWUpBQjhDQmlYTC9LcXc9PSIsInZhbHVlIjoibi9SSmpmY3VUVDI2Um1MU1NiYVkxbTI4U3BYa0FCZ2JrMWxxcHFPckVlQVlyV2prT01Fa2luQnZKT00zaUNXbU95TGFRTjJlaFhNem16VDZtQTZrRHNNU1d4WnNYT0U0OWNYVnRxaGVnczMzVzZmQ1h0OVhER0g3SzV2N1l0a09Rcys2WS91SGlWZ2dKWDVaRDBSN29ZaXpvbEU1M1BnS0R3ejNXcXU0VjJqQ2lDNEJTY0tJUDFwMUdYWHpBL1JHMEE4dVlpSmRXOTJBRzN1N3lXWGlBc3NpbGRPZFZMZEh5SlYxZmN4aHdRUG1ZNGhjSnVhRzl2TDRNQldDWnBiQUFBcGxnUXo5YTNCeGhteHZpa3B4OThkN2RHdHV2OW1xTmhiOG4rcWVxVG4wRzZHZ2FnMWkwZTRYb25FcVlnQTBXTXQ1RWdPaisvWGlsMjZjWVhVRy85VjMvUlRNZGJXWkdOK3I2MFBvMk5aV1Zab0hmeG5hTjVxU1lDb083N0l1M0xsTkpraEx5U2NiSjl1RjdPbE9FQW9mSTFhaERHcHVhODYrY2prQUhHSjhTU0VqanljOEdzMHpmWkFiSXJPYU9pb1VRaEZ6dmt5Ym5wWDRubXRUd0ZMdmQzOWYyajJaUHJDVHlIYU9kVmpMUmFDMlpjZ0dOQjUzU1dMZ3FOOEoiLCJtYWMiOiIwNWNjZTRkYTdhZWUzNGMwOTliOTA1YjM2OGZlYWI0NDYwNmM1NWFhZDFjYjE4NjM4MDZhOThjMmVjMjI1NWZkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:16:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Iml1eVlUcTZhTXU0MVlpU3dFTnJrK2c9PSIsInZhbHVlIjoiSzZhNk1zMW5Cd1ZKd2NIWm5NR09vY0cyd2NvTEUrZ2xHS1NqNFNWMitCcVJ4UUswNHJseDhJVzAxL0owRlBPWDBaemhpOFpCdmFDckNBSzhYT0duNjI0Q0dBRjB2cjNXUC9RbkZxZ21OdXZDZENhbFZHODRnLzNxWG5vTjBOVFppNkVBaE5MRFErZ3Y1eUZSUnR4YXl5ZmM1cGZXcXN0bUZITG1jaWlHeS9kYm1YUXNteCtBNU9oSzJzYThwT3RZb0hBbzJUSmZmYkNlcStISjh1cEFKV3pvcnZmaVExUTZyem5tTWZMMkVzei9DOU1tMXRMSDVjSzR5eDd2d3Nrbkh2RmRLOFZPOUo5eU8zOGEzMjJWczdPMmE0dXFuK0ZoNk11NnlXTnMvbFBFS3NZZUE0amxvUFQzOVF0TXIzcFJLdE1JaWZhSk16SmdNWklTS1psMjQ4c2liYlRMNFBaVlhVekRycFloSkJESTVySE5tN1NFZWRja053RzlJbE4rM0hYY3UwQ0twdWgwbTh5S253aE9VdnJYZ1F5YmN5OW0rdUJnMFNHc0lYWnZKUDR3citUQ0VnVHZXTVY2UVFNcy9yM0tCRm15ZGZPanpXQUxBNnZpazB5UUhrYTRMdXpQOWVwemd2ZUs1V2NoYnlhaVZySmhGQnZJQ1pnSUwvR2ciLCJtYWMiOiI1MDgzNTZmMWIxYWVjMjA2MGRiMzFlZjY4ODkyNWIwMGYzMjY0NjM5Yzg3NGNkYmE4YTRjNjAzOTljOTFhYmU2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:16:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1616585635\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1869013218 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1869013218\", {\"maxDepth\":0})</script>\n"}}