{"__meta": {"id": "X19689e7ed741108bb45db2ab09b83851", "datetime": "2025-06-17 15:25:47", "utime": **********.426584, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173945.955456, "end": **********.426627, "duration": 1.4711709022521973, "duration_str": "1.47s", "measures": [{"label": "Booting", "start": 1750173945.955456, "relative_start": 0, "end": **********.132834, "relative_end": **********.132834, "duration": 1.1773779392242432, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.132866, "relative_start": 1.1774098873138428, "end": **********.42663, "relative_end": 3.0994415283203125e-06, "duration": 0.2937641143798828, "duration_str": "294ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49205496, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03545000000000001, "accumulated_duration_str": "35.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.255773, "duration": 0.02423, "duration_str": "24.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 68.35}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.307437, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 68.35, "width_percent": 3.018}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.360982, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 71.368, "width_percent": 4.937}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.368746, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 76.305, "width_percent": 3.724}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.38602, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 80.028, "width_percent": 10.889}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3992739, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 90.917, "width_percent": 9.083}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-757021426 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757021426\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.382173, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1639943913 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1639943913\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1953696659 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1953696659\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2081011140 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2081011140\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-335365573 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173924302%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFEYm9qamxQdmduWitaQ2lqalU2Tmc9PSIsInZhbHVlIjoidkxSMWxPY0hlUllUSmIzbVNIY21WZ2NYdEJIWVhGY1Y5cE9mdDR0b0U3YngwcVhuKzdUcXR2T3NoVUxudUtESWpJQnpKTW1UZGljYkgzUjZncGQxUk5wRmU0VU1CaFNWUzh2RWwvdWFsNk41SmZ5YUJsRmloUFpWREJvNE84dWZsL2ZRWGhMOGc1ODBxckNyRzU3WmtnMGtGNHphemlIZjN6YzQwUGFwK1EzeXhqT0Nka1FoZ3BoWi9LZWh3dFBJREp5dkI4TlQ3U3R1K0RuOGIyQjhVZG9PQVNBeGdpYmJxK1AzQlVDWFVHbTNNd1RKZFNlS3FVU2RoWjhYSmlnYzcvbzIyRzFtS0FzamJQdkxsN2xncnJQRDlnam84eFJGT2pOU3FDVThpSTdia3NWUHQyeXI4WTUrWW5tdnN6dHM4dmlwQjBEUzVhZ2tCOGNnYzJJam1aV08zMUU0NVAyUHl2UXNpNjdFdnhjMUoyelRKdWd5K3JkUUpCaUVYRzJUNHI5Zld3U3A2TEhuVzh5c0NMaE9EZzhRZ3BRT2NyeGdYd2diekxiY0p6Y1ZTSUM1WDc3VkZRY0k5N2dyamRXV0VzSG0wZ0NsOHB5V3hDY0YwU1B1bEtTTkpCS3phc0NmNXhWaUgvOXBEdlIwaVlZOFE5RmxycDVTY2FMWnYvSi8iLCJtYWMiOiJhNDRiZWIxNDcxZjEzNTA4ODc0YjhhZGExMTIwZTdmNTc0ODU2MTE2OTRhNjFlZTQxNTU5MWU0ZjJjODU0YzRlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ijl4VzZESnVRRVo1bkFncDBPWTVwd3c9PSIsInZhbHVlIjoiM1ZvNkkrZzV5ZllLWi9rcVlYTlQ0cVdpRTZ2MENONUYxcGpYNkc2TU5PcXNtZWpIRldKdnl1NURiaWJ4anBzWTROUkgxbjJhUEl6OENORGRqQnJzWk9DMHdLdURFbjlJVGRVelgreU1sYUtVemJidDRGT0szZmNZQmo0S0FZNnJWeWxCK2ZsalJHbHJXSW81d2t5MnZYU1Q2Mk1nVlVzN0tkbjFxQTBJaUxWUUI4Z08wdWVqTlNJZ2txUjFFdVVueDBYRXI4ek9yMmUxVlY1Ri9MT0Y0dWcwS0J3OG1NK3YxaTFzY1ZZQW1ma2M2Q0tJd2FxUkFWdFgzL1R5dytOYnlmdWU5alRoM1NxUlk3MWxsZm1CaVN2bU9tSHdHUGFqZExCTzYxN3dyOHBXY1NKMHgrNE5lSVUrTGl2M1FqRDVxaVFaUWROU2RjRDdYbXlRNktwREZMd1VzanpFVDBsbFFqSUpoZmdEOWkweXd1b1FJdm1wbXJGYUZFaUhyZm9KZXcrbStBRGRiLzBmeGtTcXFtd09IUG9WTGtlKzkxY3QrNGsyM2F4dFNsOTJ3MzlNOXU4NlJIMXprTC9KUGJuM28zMHhjNkhoR3U5c2RSR0JRN2JuaXFNSFQ1V0o5S2JleXBxTDFUN3d3dGdmTWUwbDV3V0dYK2o0alZMV2lmc28iLCJtYWMiOiJkMWFmNWRlNzNlMGMxNDUzMGFjOTkxNmJhOThjNDgxOWU3ZmIxYjU2ZjEyZmZhOTgxNjg1ZGRkYmVkMTg3MGVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-335365573\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-744078463 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-744078463\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-34964572 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:25:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdMbVJXN293dUFNeDVyUXk5bmZlWnc9PSIsInZhbHVlIjoiMlJQSlNMR1BxVm1rSEw0UjhiOEljYi94V3p6cmo2Z0J6YUp3UVNzN2lGb2FKejBKcFY1NVZNNXErUTVYQ1F6NVA3ejl3VVczRXU5dFlib3k3aUI1YUU2ZVNKbjhTTlpqeTI3RzBHakw2K21jYW9yRHg4NVlERWxRUXJTblhpS09abG1QMXY1RmUvQUxzOG5JS29VbVZwaFozQ1lkR1BDb0lWVmthNS9POWNFTjZMZ1BhN09HSzVLM0hBT0ttSHp4b2hldWg1akJ6WitxVklCQXpuZERjSllKQm4zQzhpeGZMdGhFN0czRHRwU0RXRXNtTys3R1E0Z3NlR2JaRzlFWHA0bmsvNGJlWXgrR21FdnBjT3R3RUlMZjZJQks3ZGpWem9NUzdoZ0NsYVNkMmJnajVjQU82NE5Fb0FXMXhPNUg2RE05RlAxL255YjBsMUoyeTZvT01PeFZldmNLSDEwS1lSSU1UTE42TkZMZHdTdnhUQi9NelhaOTcyZjVMOTJqY0JVZnRncnBHeXd3K0RZOVBrM2gweU0wZy9MeE9NbHJPNk5ib21ERWt1WnZDbDZNMWpreGVIanBiRGhxdFgrcURGWGU4WVhHUjduVjduK01taGdlR093Z3QrYm54UXRSdnF6eDZrK0drQjk0Z3gxYXNwWU9rclV5dUVsSFlXRlMiLCJtYWMiOiIyZDc3MjVjZDNkYTJlN2JjYjE0YzYyY2JkYjM1N2VmZTFkNTBkYjlkNWRmMDI3NzlmYjE1OWJjNmE2YWZhNDMyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:25:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InRSRENuNlB5eHQ5a1dsRmhGMmFzRlE9PSIsInZhbHVlIjoiN0FuNDNRNVUwZnl1Umx3QmpSaEtzRDlUNncwSUluTGswaXYzOUR2Y1ZYYjgyWW0yODJHUm1JUkdjQ1VPSlNZTWdiMFUyYVpneU56RVVpTXIxZ2FLbkNPUGFwL0FRRnhPNzcvejMvOXFnK0Y5ZFd4TGNLMnB5LzNpbGhpcVNGc1NGOHdhL1I2OVd1TkhtalR4cFdPV3N0RzBCWVNlelBRQzNPRnM3Rkl0T20zUDVKU3kzanJGN3BsQ2h0U0hDc0Y5WHl3Tkkwb0VaaVJuQ2t5SDVrenF2QmQ5WUVxQ1B6bTFWT2JBbW1ZdFR2QlJKVHhzdGJwQ2liZ0pVY3RQYUdGengwSmU1b2RrMHRiN3FsNTRLV3oyNk1IY29BWjdyUEYxRURJa3hYVTVpeWJKZVhpZ3VUREd1UUIwa0NQUjFwdXRKSU5nZkhJOVdmdnh0STJvU2xuSlhpZW5kam9PVVNpSGd2SnJEUHZXcEtNTlNtWGRaZ1VwTFpzQTU1Tm5LU2tYMTBYODFnYmNuM3FGZndzT0R4UFpaOWJvbFRlSkhsWFJHWldXMW1CL3M4UDBDMVY3RFErM0pmWU9ERzE2bG9ieXlJL0EzN294OTFSRWRacHdwaXFqZk80WW92a0JqNUU0ajI4OXVHa2lZNnV4a04vd2wrNzVLenVtK3Y0MnFDbnoiLCJtYWMiOiJiYTg3MDUzM2E5MzBhMjE3N2JkODEzNGVjOWI1N2RjYzg0ZGMwOThlYTFkMDE4ZjcxNmMwMzQwNmY3NDBiNDAyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:25:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdMbVJXN293dUFNeDVyUXk5bmZlWnc9PSIsInZhbHVlIjoiMlJQSlNMR1BxVm1rSEw0UjhiOEljYi94V3p6cmo2Z0J6YUp3UVNzN2lGb2FKejBKcFY1NVZNNXErUTVYQ1F6NVA3ejl3VVczRXU5dFlib3k3aUI1YUU2ZVNKbjhTTlpqeTI3RzBHakw2K21jYW9yRHg4NVlERWxRUXJTblhpS09abG1QMXY1RmUvQUxzOG5JS29VbVZwaFozQ1lkR1BDb0lWVmthNS9POWNFTjZMZ1BhN09HSzVLM0hBT0ttSHp4b2hldWg1akJ6WitxVklCQXpuZERjSllKQm4zQzhpeGZMdGhFN0czRHRwU0RXRXNtTys3R1E0Z3NlR2JaRzlFWHA0bmsvNGJlWXgrR21FdnBjT3R3RUlMZjZJQks3ZGpWem9NUzdoZ0NsYVNkMmJnajVjQU82NE5Fb0FXMXhPNUg2RE05RlAxL255YjBsMUoyeTZvT01PeFZldmNLSDEwS1lSSU1UTE42TkZMZHdTdnhUQi9NelhaOTcyZjVMOTJqY0JVZnRncnBHeXd3K0RZOVBrM2gweU0wZy9MeE9NbHJPNk5ib21ERWt1WnZDbDZNMWpreGVIanBiRGhxdFgrcURGWGU4WVhHUjduVjduK01taGdlR093Z3QrYm54UXRSdnF6eDZrK0drQjk0Z3gxYXNwWU9rclV5dUVsSFlXRlMiLCJtYWMiOiIyZDc3MjVjZDNkYTJlN2JjYjE0YzYyY2JkYjM1N2VmZTFkNTBkYjlkNWRmMDI3NzlmYjE1OWJjNmE2YWZhNDMyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:25:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InRSRENuNlB5eHQ5a1dsRmhGMmFzRlE9PSIsInZhbHVlIjoiN0FuNDNRNVUwZnl1Umx3QmpSaEtzRDlUNncwSUluTGswaXYzOUR2Y1ZYYjgyWW0yODJHUm1JUkdjQ1VPSlNZTWdiMFUyYVpneU56RVVpTXIxZ2FLbkNPUGFwL0FRRnhPNzcvejMvOXFnK0Y5ZFd4TGNLMnB5LzNpbGhpcVNGc1NGOHdhL1I2OVd1TkhtalR4cFdPV3N0RzBCWVNlelBRQzNPRnM3Rkl0T20zUDVKU3kzanJGN3BsQ2h0U0hDc0Y5WHl3Tkkwb0VaaVJuQ2t5SDVrenF2QmQ5WUVxQ1B6bTFWT2JBbW1ZdFR2QlJKVHhzdGJwQ2liZ0pVY3RQYUdGengwSmU1b2RrMHRiN3FsNTRLV3oyNk1IY29BWjdyUEYxRURJa3hYVTVpeWJKZVhpZ3VUREd1UUIwa0NQUjFwdXRKSU5nZkhJOVdmdnh0STJvU2xuSlhpZW5kam9PVVNpSGd2SnJEUHZXcEtNTlNtWGRaZ1VwTFpzQTU1Tm5LU2tYMTBYODFnYmNuM3FGZndzT0R4UFpaOWJvbFRlSkhsWFJHWldXMW1CL3M4UDBDMVY3RFErM0pmWU9ERzE2bG9ieXlJL0EzN294OTFSRWRacHdwaXFqZk80WW92a0JqNUU0ajI4OXVHa2lZNnV4a04vd2wrNzVLenVtK3Y0MnFDbnoiLCJtYWMiOiJiYTg3MDUzM2E5MzBhMjE3N2JkODEzNGVjOWI1N2RjYzg0ZGMwOThlYTFkMDE4ZjcxNmMwMzQwNmY3NDBiNDAyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:25:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34964572\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-690064004 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-690064004\", {\"maxDepth\":0})</script>\n"}}