{"__meta": {"id": "X485dccf2f9aafd5f024de6d932250d51", "datetime": "2025-06-17 15:16:01", "utime": **********.154546, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173359.715661, "end": **********.154582, "duration": 1.4389209747314453, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1750173359.715661, "relative_start": 0, "end": **********.857212, "relative_end": **********.857212, "duration": 1.1415510177612305, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.857231, "relative_start": 1.1415698528289795, "end": **********.154586, "relative_end": 4.0531158447265625e-06, "duration": 0.29735517501831055, "duration_str": "297ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49205912, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.030680000000000006, "accumulated_duration_str": "30.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9780018, "duration": 0.018170000000000002, "duration_str": "18.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 59.224}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0275779, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 59.224, "width_percent": 5.248}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.082746, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 64.472, "width_percent": 5.737}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.091491, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 70.209, "width_percent": 3.814}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.109642, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 74.022, "width_percent": 13.787}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1238968, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 87.81, "width_percent": 12.19}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-917692890 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-917692890\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.105688, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-890072503 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-890072503\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1526762379 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1526762379\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1098536921 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1098536921\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBIanpGZ2ZWcmZGNHdyNnNCQTNGaVE9PSIsInZhbHVlIjoiQ3RhMnY5OHlDSXJrNGpKZDVHR0w0c3MyZXNxRFlpMkhacFpOdnlwZ0taUFYrZE43QXovK3N4NHpTSXh3RGdoYVNyQTBCeXJPU3RhNEJwSm9TMVZkV2VDcTFaa3ZkWldnUVRqbDd0MTdiRk1YQU85N0NZWEhENDZ2SVl3NXhiWFZmMTlrbndmZC80ZVVVSUR1NmNreVh0Qmt6RjltcUpjZERLV28rcWR0QkpxYUFndWVQWkhkTnpsSUgrNmtTZlRNd1owV2dxaUVhUzdNUmFQaFR3cExTc3B3Mk10aXNCWDlKTzh5TFZ4VVFITFg3MXNDNk45VjhQaWZnQmpyZy9QVUJLTDlUbzhEOUMwUGY5UjE0bDk5dUZrNjErQWM1Wk9hbWpFbVhRbnpneEVja2VKZm0zMnNneGgyRDF6V2g3SXJMaG9RS1NvMTF4dURLTjJCUGI2ZXFqRTVSUXJZNVN0aURWZUFHSEpuQTNDeGxmTHdTdVdCbWZBc3lrdUNJb0RBRWFpTG9ScWh3N1U3ZGlCZll4Y1FsVU1iRXJiaUp0STVRcXNKdUxQSW1aZjZVVzZ6WjEraENIQUpOYy80TFZJS2FxSC92ZERwd2ZHdW1BaldER2RwSHZYaHNBdEFzWXVzSVhQMXc2ekN2V2JldSsxQzlSelUzSVpXZUxyQTI0SlkiLCJtYWMiOiIwYmFkYWE5MGYwNDY5M2E4Y2FjNzI3MmUyYWRjMjA0OThlMDU4MWY5ZjQwNjFkZGQwY2Y4OWY3YmNmNGUxNzM2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjhGRHVKUnFrUEkwcjlNemU3aUlrR2c9PSIsInZhbHVlIjoiMW9qUitRRUdRZFVpSFArQVpBbFdsMnJrZEtMWW1vSjhPUFZETDdkNnl2Q21NMm5oWVlKUVFWU0ZUOC9JeVBMRkhMOTJmSTA0QW5mRi8vTXlJRUI1S3FkQ2EwcEpEZXVTU3psamt0c3B5RDlOYk8rKzJiVTlDcENaR2Q4bTJrd3FycWtaQ2N6NVBYbUZFNTl0TjcrVHgva0syQk43dmNxelUvaFlqOUNJaFZzNzhjMVZhSEtMd21pd3hZQWRMRGthMVFCZ1p6YTJDaWI4azNnVDFzUlVBd0QrU1hSNTArVnBhYVFIRHNGdG9oczRmbURCMTF0Q1ZXVDNYWG9IaUI0a21XWjZWN2t2Zy9wbys2OE1xMlZSYmk0eHVVTXhMNWRwRmRqT0RjMWFMeFVhcm9HMmZCMFo3aVVkZzhGRzRmTlBab0lLZkc1c2xyL2hIb2JWVWczMFVVV3RTdDNjZGFEMXk0d3ptblltdVNRdXZ0SFdCcW9NVVYwYjZ3a2doMERhTUpwS0NqNXYwUkJDd0FyRGlmdGlMMnN2a0JsTit0SWhZN0I0MUVSUTJVVVNnWW0xVUxvT0I5ODRXUmVmTE4vbEZ5VjZoZ1RDVUZpT1EyTjJlMzd2ZEplMlJDZnZHRERaUXNGTFNGRDRjQVZDYk43L0loN3A5QW1FakxrZkVVN2UiLCJtYWMiOiIzYzg4NDdlYTJiZDFhNGVlOWU5YTc1ZGQwMmYxNjE4NjU3YzNhZjFmNjc1YzI5OWEzY2EzZjMxNTQ4MTU5M2FhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2119178311 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2119178311\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-976713716 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:16:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjR2VG5qWmZEOHRkemVmU045M3VQa3c9PSIsInZhbHVlIjoiNU1PZmR1M3pWanVLWE9sK3dmYmlYMGhHU2gxU1dlY1Z3emM4czRNb0VFekNENk5HcnhqYU1VV0k4QUMwcXY2OGM4Ny9OeFJXbmNpb3lZTU9PbGtwMEhuY1FHRDh4N2I0TzRQV3FzT3EyTm5ad1RNcm1reWwzbzJSSm44Syt5UDZKYTZHcm9UNWVnOTArdXNyYzdKN3NsdzlhZVp1cFNwQ2tQVGxVUGFueTJxQ0YyK0JJYlRHbUJJRUlKOVpraXI3elJ1M3g1azJ6bGVuczF1TTMyN0taakNNNTF0dFBJaW1iU1lMRElYNE12V1lTZ0xrQnVlak1wN2ovaElLdlNhd21QY2JkYjBkS0REb04wcHJvNy9NZlhwV2Zqc0tBS3Z4QmdsckhxZTFYdDhOQjVseFF6UHFEYXhweWRvY3I5NE1zWnhCWVFwdVRacFdpTXMwYlcybWFsQmpqNlB4WXlWWE9RQWVmT1cvclJGeFAwdUxCMWxKSGRGVmJkVGg4SGJ2Q0F4Y21ZWHVqeGZvYWZidlh1Rk1KR0tzMGplQm5BbEd4TGtjYS9iMU9YWDNUaXFkZ3BjcERlV2hPeEI0YXlNUlhmejRzZVk1dVZlWXpiMlJHenNpV0w2RTl1NzFBM0ZRYlo2V1l1V1MzdlJSbVQvb0Q5bWtJTUgzVmFtMk1YR2wiLCJtYWMiOiJjOWQ4NzY5YmJmN2U5YjUzNjk2ZjhlZjBiZDYyNWUwODMzZGM4NWI2NTY4YmQ5ZWI4YWNiZTQ4NDM1ZjlmZjIxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:16:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkRIWmVDaGd5OS8wN2luT2drM3RqaFE9PSIsInZhbHVlIjoiZS9HN1hKck9zZlI1MVZ6R0ZSQkV6L0FNWUh0U29JVjZGMHRMNTVna2YzUkxlYXZEcy91S2lkdFhZdnZCVS9jTTEzY2VUWkoxY2JMNW9FZndXMmNxMnNsWnNsb0RTWm9rNEJPYXNOdTcvU2hyTjI1bVQwMFl6MmUvam43MWEzVjNkODgxNS94SDZjMXVEVXhZajU5Y0dBSy9pTm50TU10YWRXQVFSOWh0ZEVaMStsd0JYdkJOd1BlOXJNa0tvOWx2SWw1L0RLVkhnTHVpRGlCcFY1WVJFWnhJVEpyN1VQZnNEd0xDSmNpR2FIVTVkc2NoOVRHbTdLZldSbnppR1BMMTZLbjUxQldRWFJRTjBOUGh5Um03cTNnVDd3eFhUa3NSMTlSaXZpelkvUXJGWUtWTTNNeUNzWm4vQ2VoZTBQYWV2TmpiN1dJb1BWbGdRN1UvSWJja0JqdWtYTXU5ditZc2M1NXNJVXlvd0lCNForU3plOUNGYzRaWDNCV1o2RlVYbFhvVWhRemZpZnhJUWR1OXRpaU1JNW9uOCtXTGdXMWczdE0zUUFDSFVmaTFzUW5vTHdYY1EySXlDdXpPcUNYQmltZUh5UHFHZUZhbDZJZlU2dkhETnFBQVNzR0REWmNBUWJBeDZ3dnBuVDNhQTNKSzAzK2FhejdsQngvdnYvdnUiLCJtYWMiOiJiNTg1NzhlOGZjOTA3ODExOGE5NDVlZTJmZjY2ZjE4Y2I0MzRiZjgzY2M1MTZkMzNkZTM1NWM2YTAzNzk1NDlkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:16:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjR2VG5qWmZEOHRkemVmU045M3VQa3c9PSIsInZhbHVlIjoiNU1PZmR1M3pWanVLWE9sK3dmYmlYMGhHU2gxU1dlY1Z3emM4czRNb0VFekNENk5HcnhqYU1VV0k4QUMwcXY2OGM4Ny9OeFJXbmNpb3lZTU9PbGtwMEhuY1FHRDh4N2I0TzRQV3FzT3EyTm5ad1RNcm1reWwzbzJSSm44Syt5UDZKYTZHcm9UNWVnOTArdXNyYzdKN3NsdzlhZVp1cFNwQ2tQVGxVUGFueTJxQ0YyK0JJYlRHbUJJRUlKOVpraXI3elJ1M3g1azJ6bGVuczF1TTMyN0taakNNNTF0dFBJaW1iU1lMRElYNE12V1lTZ0xrQnVlak1wN2ovaElLdlNhd21QY2JkYjBkS0REb04wcHJvNy9NZlhwV2Zqc0tBS3Z4QmdsckhxZTFYdDhOQjVseFF6UHFEYXhweWRvY3I5NE1zWnhCWVFwdVRacFdpTXMwYlcybWFsQmpqNlB4WXlWWE9RQWVmT1cvclJGeFAwdUxCMWxKSGRGVmJkVGg4SGJ2Q0F4Y21ZWHVqeGZvYWZidlh1Rk1KR0tzMGplQm5BbEd4TGtjYS9iMU9YWDNUaXFkZ3BjcERlV2hPeEI0YXlNUlhmejRzZVk1dVZlWXpiMlJHenNpV0w2RTl1NzFBM0ZRYlo2V1l1V1MzdlJSbVQvb0Q5bWtJTUgzVmFtMk1YR2wiLCJtYWMiOiJjOWQ4NzY5YmJmN2U5YjUzNjk2ZjhlZjBiZDYyNWUwODMzZGM4NWI2NTY4YmQ5ZWI4YWNiZTQ4NDM1ZjlmZjIxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:16:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkRIWmVDaGd5OS8wN2luT2drM3RqaFE9PSIsInZhbHVlIjoiZS9HN1hKck9zZlI1MVZ6R0ZSQkV6L0FNWUh0U29JVjZGMHRMNTVna2YzUkxlYXZEcy91S2lkdFhZdnZCVS9jTTEzY2VUWkoxY2JMNW9FZndXMmNxMnNsWnNsb0RTWm9rNEJPYXNOdTcvU2hyTjI1bVQwMFl6MmUvam43MWEzVjNkODgxNS94SDZjMXVEVXhZajU5Y0dBSy9pTm50TU10YWRXQVFSOWh0ZEVaMStsd0JYdkJOd1BlOXJNa0tvOWx2SWw1L0RLVkhnTHVpRGlCcFY1WVJFWnhJVEpyN1VQZnNEd0xDSmNpR2FIVTVkc2NoOVRHbTdLZldSbnppR1BMMTZLbjUxQldRWFJRTjBOUGh5Um03cTNnVDd3eFhUa3NSMTlSaXZpelkvUXJGWUtWTTNNeUNzWm4vQ2VoZTBQYWV2TmpiN1dJb1BWbGdRN1UvSWJja0JqdWtYTXU5ditZc2M1NXNJVXlvd0lCNForU3plOUNGYzRaWDNCV1o2RlVYbFhvVWhRemZpZnhJUWR1OXRpaU1JNW9uOCtXTGdXMWczdE0zUUFDSFVmaTFzUW5vTHdYY1EySXlDdXpPcUNYQmltZUh5UHFHZUZhbDZJZlU2dkhETnFBQVNzR0REWmNBUWJBeDZ3dnBuVDNhQTNKSzAzK2FhejdsQngvdnYvdnUiLCJtYWMiOiJiNTg1NzhlOGZjOTA3ODExOGE5NDVlZTJmZjY2ZjE4Y2I0MzRiZjgzY2M1MTZkMzNkZTM1NWM2YTAzNzk1NDlkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:16:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-976713716\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1541287152 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1541287152\", {\"maxDepth\":0})</script>\n"}}