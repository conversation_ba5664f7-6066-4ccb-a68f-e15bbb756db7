{"__meta": {"id": "Xefdc3b2325abde3934c5d29548a999f4", "datetime": "2025-06-17 15:31:40", "utime": **********.219067, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174298.833219, "end": **********.219099, "duration": 1.3858799934387207, "duration_str": "1.39s", "measures": [{"label": "Booting", "start": 1750174298.833219, "relative_start": 0, "end": **********.080742, "relative_end": **********.080742, "duration": 1.2475228309631348, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.080762, "relative_start": 1.2475428581237793, "end": **********.219103, "relative_end": 4.0531158447265625e-06, "duration": 0.13834118843078613, "duration_str": "138ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44978768, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02136, "accumulated_duration_str": "21.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.167856, "duration": 0.01984, "duration_str": "19.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.884}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1985822, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 92.884, "width_percent": 7.116}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  8 => array:9 [\n    \"name\" => \"tea-شاهي\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"8\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1764652700 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1764652700\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1195678132 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173975623%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IitkVnJ5V2x3MU5VbjZpcXFhZFZQUXc9PSIsInZhbHVlIjoibDRKRGN0T2FPTmxNYXJRbEU3WkZsbXRqK1lTa2c0c1ozLzV5SUhyYnF2REhTamllSTR4c3JmUStIMHBZZ2pMdFpKOWNIYXF3cDFEdnpLS0YyTFFhMmU0czlpZXluOFFsdGk5bWJrRWtRd3kycFlWUzNSd1VzT3hCU3FiVGpGN1NiTzV5ZXBhOXFUNG9xdmtQd3NUSXM4bVF0dllpd1J2eWhPOHRJTXNBc20wR0FlUWpZS1pxR1k1V1Rwem5oZVBKY2l6dWtYZzZEdExweVVNTWZzSHhXZktoaVNPUkJPaEpuNUFsNWtqZ3JWa0s0Qk0wWHlOVkNmTENwNUdhU2t4QzVyRmJaRWM1YkZqZmVxczV0WHhCc0RwTFVTenpKTlZiaVZCV0g1QUh6Y2NtaVJodkRIU3czdkRyL3kxc3JNdzBuNzJMamhPK0M4TWZVSzhtSTNjdXNrdUNBdC81WkVJeEZwYUxJWXh3NVloMWo5cWJKOUtoTHB4c09SeHZ2NlFEcVRpcXMxTGZzRFhWMmQ4clE4OVJhb3k0QUpOelo1ZlZDSERqNHYyL0F1VU8rQ1Z1dVpWUEl1SXNYMld5b0V6ODA1SnR2Uk1RRndmaFdXRXJWUWRIa0J1YjNDbXhjOVZNRk9yejNKVnl2VVd5RFVuQ1NoZGlvV2ZTbmk0Z3ZvV1MiLCJtYWMiOiJiZWYwNzUwNTkwYmI2ZjhiNjljZjM5ZjViYWJhNDA3ZWQzZTViMzg0NzY0NDdiYWRhZjdkYWM5ODBlYjdmYmY3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InBqbW16WW5sTzNXK0NzZkVoTEU0M0E9PSIsInZhbHVlIjoiMWtmU00yTWd1QjR3R0JxV3ZLUDdlT2x3TGkwalJqYUFPREYvak5wa0N0Y1llc0prNTEzSlN5dk1lSElBTy9uay9JV1hmQTdpQWxFd2dUd09kSUllTjFMNk9DSHVOZlRkR3dkbE1Ud3BWemRrSHgwMkRCVWViamhGZjV6NTg2QnhzZWlhaXpVaVRjdkR1aGNuS2ZpZVV6Z001aDV5VEd0enV1R0hqWGd3RUxzZUEvV242cVJvSFdobXFOeWVMNUhNem1VRHBhM0FJOUptOG4zMjQ2V1J5VGZSclcrcWRyMTVQd1lmZXdkRWR3b3FCVzFqc3NVeXh2dEQwVmNhOGRVendmT2VpbFlJV04rTzRUMndpNGRTeWlYN2ZESXdrd2hEUFBjQXVNc1RsQjJZWVZGRTlqcWIxWEtqRExrcWdaYXB6TDBvVkVFY1YrWWxDdm41TFJuN3VkcWVqYVh0aWJWTzM3UUF2ZkFGNUNRWWZqWnlQMXRudlhjM3orZXRJbWcvL0xTU1hOTDNZUkZGS3NtY2dqY0V4eDNrTlIzOHgxVmNoOW9XdS9vV2hIa0ZNNGkydlQ1TGR6RTh3Wm1wWVZjNHFJRVJZU0FDaE9XaEVkM3FzQjhWNVVNRW5od09SMnZDSnUwdXlITGxxRmlyb1haaVBYVy85ZFJNTmtDTmRsTEkiLCJtYWMiOiI1NzUyNDM4Njg4ZDYyNTYxNDQwMWE3ODBiZGVhNmVhNmVlNzYwYTc0NTQ4MjQ3OTI0Y2Q4MzVkM2U0NmY4MzgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1195678132\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-49560583 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-49560583\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-832738433 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:31:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRxZ09pYWZCRVBHMENXMkNuZzNWVGc9PSIsInZhbHVlIjoiYkZibWdQa0FNMmpqdDY2dVBXRHNCVVp1ZFdKTHFrSFUzaWM1Zy9yTzc2ZjRrUjdpNTBWaHdJWHRvWTJNcUxMK3hYRVh3Rjk0N2VSTHNQNGtkOWV3ZVZReDBaeGtPUUE3K1U1V0x5MXgxSmlhZFVBaGV3SzM0OGc5L2RYcTF2TzJYRXRaakdmTDhtcEN1WFdXWXNYOVAybWtUWHVWYWlpWXYxNzhRcW02aU1PTnJjUVRDT3ZLU0JZd28wWmJkc25tUXNONlo3SlBQNmROK3pHR2loWk83NTF2NXh5UmpzbTVrY2VscXFmSmlUOGFmUXpndUJBdFkyRTZzWUJSc2huMm05THkrdG52VldXMzgraGIvR01uN092bDVha2x6cFdaVW9xSGdKMGoySkpZM3JuR3BoajNMOTFOS0xlSlc4OXZhZXdIaDZSVTRtU2h5QlFVVUM1REtIcG4wdUw1YlM4QTA3RVo3ZjhvQTRQckhRT3RJS28xbFMwM3FuOW9yWlpycHdKNTVNcFN0dHdhMjF6bWIvRDkzNVBQTzBCemdNQ2hBb0M1YWJ2bXlnUGRwV2pzbjNiMFFkY3kzNTREVk4xT1pwbEFmZ1Vqb3ZVZ2ZFRlRHRG5zNGcxTXRSVGZVWkdaR3RDeDhSd0p6bXBYTENWeHNuNGliUVJqcGRYSmlMN28iLCJtYWMiOiIyZDFmZjNjYjdkYzJmNjI4ZDI4MTgyNGNlYjIzNjViODhmZmNiOGE3OTBhMDk1ZWFjOTgwYzBkMmJlOWMyOGQ1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:31:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjMrUjNrMWVDc1JNMUVQOXp0YlFJV1E9PSIsInZhbHVlIjoibzhHUStvbjJmY2Mwa3JuWmNtaEFhazVoVDBqM3JSZGdZckgycXNqNThRZDgzbWdoeVdod05pYVlUSnV4VlM4WWN0a1lkNW9nVHkzeHNORmNFUXVMZmR6eTBNM0o4LzBHMDdlZkVuTElSeE9WN1pUWFBBRmtaeWlQZTkrRVovcHdNRUtyWkZuZFh6MDg0S1ovSVhhbklqVEwxcHF5c3JBcitIaSt5b1BRSVpwaTVrQnU5U2hGdHl1RG80NlJ1aWtIbnVRcTJqU2JvQVpzdW1zVElsZ2FyRlhPYTlnU3NuTUZ6VktOcXhadDc3T2hNbDMxaVh6YlFkbmlFemxMUnVSZDVYTERuN3Y1M0xmYm5DM0haR04rNHorZlA5cUNrL3h2NnZOenFjUGRxNTFVdWpKV0JlUEZOMTdDV0VwbnI5aHFNOVk1c0J4bDZySHhscldpa3VxcnBldW9vaExBdVdvU1pyVUhDMW5WV3JaemM0YkwxcU9ndEZjQloyU0tVaFJDaVp1SXlreXIzTEsrMVRXK3A5eUozVW9aY1laQzFZaXVkY3JRT0QxTU9ETFdSUlZDMytUZmw2QTJ5T21iT0gwMEErRTRERTF1cXBPSEl1NGtUQm9KQytDcS9KWW0rY3dxZ0N5bG15VlV1Qkl1SFlMc1pGWEM2ekhNN3kyMHhpbDQiLCJtYWMiOiJhOGZlYjc4ZGU2NWYwNmRkNTg1YTQwZmNlMTRjNTc3MjY1YTliMzA1ZTRmNTFlYzY3YzMxODIzYWVkYzBjYmVkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:31:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRxZ09pYWZCRVBHMENXMkNuZzNWVGc9PSIsInZhbHVlIjoiYkZibWdQa0FNMmpqdDY2dVBXRHNCVVp1ZFdKTHFrSFUzaWM1Zy9yTzc2ZjRrUjdpNTBWaHdJWHRvWTJNcUxMK3hYRVh3Rjk0N2VSTHNQNGtkOWV3ZVZReDBaeGtPUUE3K1U1V0x5MXgxSmlhZFVBaGV3SzM0OGc5L2RYcTF2TzJYRXRaakdmTDhtcEN1WFdXWXNYOVAybWtUWHVWYWlpWXYxNzhRcW02aU1PTnJjUVRDT3ZLU0JZd28wWmJkc25tUXNONlo3SlBQNmROK3pHR2loWk83NTF2NXh5UmpzbTVrY2VscXFmSmlUOGFmUXpndUJBdFkyRTZzWUJSc2huMm05THkrdG52VldXMzgraGIvR01uN092bDVha2x6cFdaVW9xSGdKMGoySkpZM3JuR3BoajNMOTFOS0xlSlc4OXZhZXdIaDZSVTRtU2h5QlFVVUM1REtIcG4wdUw1YlM4QTA3RVo3ZjhvQTRQckhRT3RJS28xbFMwM3FuOW9yWlpycHdKNTVNcFN0dHdhMjF6bWIvRDkzNVBQTzBCemdNQ2hBb0M1YWJ2bXlnUGRwV2pzbjNiMFFkY3kzNTREVk4xT1pwbEFmZ1Vqb3ZVZ2ZFRlRHRG5zNGcxTXRSVGZVWkdaR3RDeDhSd0p6bXBYTENWeHNuNGliUVJqcGRYSmlMN28iLCJtYWMiOiIyZDFmZjNjYjdkYzJmNjI4ZDI4MTgyNGNlYjIzNjViODhmZmNiOGE3OTBhMDk1ZWFjOTgwYzBkMmJlOWMyOGQ1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:31:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjMrUjNrMWVDc1JNMUVQOXp0YlFJV1E9PSIsInZhbHVlIjoibzhHUStvbjJmY2Mwa3JuWmNtaEFhazVoVDBqM3JSZGdZckgycXNqNThRZDgzbWdoeVdod05pYVlUSnV4VlM4WWN0a1lkNW9nVHkzeHNORmNFUXVMZmR6eTBNM0o4LzBHMDdlZkVuTElSeE9WN1pUWFBBRmtaeWlQZTkrRVovcHdNRUtyWkZuZFh6MDg0S1ovSVhhbklqVEwxcHF5c3JBcitIaSt5b1BRSVpwaTVrQnU5U2hGdHl1RG80NlJ1aWtIbnVRcTJqU2JvQVpzdW1zVElsZ2FyRlhPYTlnU3NuTUZ6VktOcXhadDc3T2hNbDMxaVh6YlFkbmlFemxMUnVSZDVYTERuN3Y1M0xmYm5DM0haR04rNHorZlA5cUNrL3h2NnZOenFjUGRxNTFVdWpKV0JlUEZOMTdDV0VwbnI5aHFNOVk1c0J4bDZySHhscldpa3VxcnBldW9vaExBdVdvU1pyVUhDMW5WV3JaemM0YkwxcU9ndEZjQloyU0tVaFJDaVp1SXlreXIzTEsrMVRXK3A5eUozVW9aY1laQzFZaXVkY3JRT0QxTU9ETFdSUlZDMytUZmw2QTJ5T21iT0gwMEErRTRERTF1cXBPSEl1NGtUQm9KQytDcS9KWW0rY3dxZ0N5bG15VlV1Qkl1SFlMc1pGWEM2ekhNN3kyMHhpbDQiLCJtYWMiOiJhOGZlYjc4ZGU2NWYwNmRkNTg1YTQwZmNlMTRjNTc3MjY1YTliMzA1ZTRmNTFlYzY3YzMxODIzYWVkYzBjYmVkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:31:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-832738433\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>8</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tea-&#1588;&#1575;&#1607;&#1610;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>8</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}