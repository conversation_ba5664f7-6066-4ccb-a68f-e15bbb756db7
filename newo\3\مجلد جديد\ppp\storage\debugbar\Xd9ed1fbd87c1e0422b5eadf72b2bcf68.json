{"__meta": {"id": "Xd9ed1fbd87c1e0422b5eadf72b2bcf68", "datetime": "2025-06-17 15:16:13", "utime": **********.386295, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173371.928191, "end": **********.386337, "duration": 1.458146095275879, "duration_str": "1.46s", "measures": [{"label": "Booting", "start": 1750173371.928191, "relative_start": 0, "end": **********.212231, "relative_end": **********.212231, "duration": 1.2840399742126465, "duration_str": "1.28s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.212252, "relative_start": 1.2840609550476074, "end": **********.386343, "relative_end": 5.9604644775390625e-06, "duration": 0.17409110069274902, "duration_str": "174ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46094056, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01983, "accumulated_duration_str": "19.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.294122, "duration": 0.01728, "duration_str": "17.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.141}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.340168, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.141, "width_percent": 6.404}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3601248, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.545, "width_percent": 6.455}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-509779092 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-509779092\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1608511518 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1608511518\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-357856410 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-357856410\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-372719291 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750173364129%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1hN21mU0p6WW4wcTlQWEJmVlJxbFE9PSIsInZhbHVlIjoiMjN2eXVrbjdPM2dha1gvRmtCZTJ0azd0OXU2N25saVZrN3NvMzNJazBNRlNlcEM0bGxyQUw1K3p6MjBIV3BsYnJiWnlhTGZMZkJBYXBRR2FINEp4Und5TSsvd1hhV1I5Tzg4V1NrNkE0dFBiTjcvZXlXSER6OXhaRUEzKytzZGdSa05sajVRMGovTnp5STJ5QmJrbUlhb0hoZmtuVWkrWElhUkVJaXBzS2pHTDJvejRZZHhxNW5EanNrOFo5RFBDWk9pOFludHNxZUI3ZWFZY1hqOURXbmpRTjNKUjdLeW1qMTExZGtGZVFWYUhEQ2tFc0VmeWc1NG9WR0xwb2FIei8yVlByMktQSFVicDdQZU9BMEhHM3FSOTFYNm5rZzJua2gyeFVBZUNZMEsyanNuMjBzMEFGeDJVTVZ3elNpMTFnVm5rOUdtUnNQS01TLzQvS3hmaGx5Q2lzWkFNaXVRT1pxdldmcXcyVDc4aDlJZ3JaZ012bHBVSmk5UXJTb0hWZ0xYVVV1RW5oM3JGeTFaOHdQNk5YaGVQR0lJMUJkUGpHQXowZDgwVjhKZkxHWFREbjNKelhmejBkMjc4Z05hd1V5azcwRDR3MTVSQmFwT29GaGhxdlZXK3hIRlVZMWpoL1hINnc2Vk1pNlB1Uk5GUTVLaTBNak9iQXllbXdOa1giLCJtYWMiOiI4OGUxZjk1YmVmZDQ3Y2UzNTZjNDVhNmNhYTQwNzNmYTNiNzIyOWE5MGY2M2JkMmFmOTRiMGUzN2NhODEzY2UwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjhtSVFpYkYxWEU4YVB4UG5zRlp3Qnc9PSIsInZhbHVlIjoiRmJCbVlEN3dqUUwvaTdkV0xEeTNqZTlsd1hZckttUXo1U2tzMTc3Tlhmck1lVFdQMVhTb2ZhdkxkQjFnaVhYTzVwTHVNbHBFMG9Fa295ZTNkUzFRanc2RkIzVGVPTG1RUVo0M0FQODZtY3hnZ1h2OXhpNi9yTDZQK2hXc3hHeXZ2VFgySmNROTJOcjgvUkZZdzROZUlETkQ1N2xnWXBkMlozS3JsWkRueHI0ZDdyOWdyZy9tRmZJZ2dnaXpBeUx6cEdiVDVEclFVeGpWbFJrdDhmWFpheTh0UGxPUHNZRXpablk0K2tqZWZSczJqS1ZXdmpxWXlLUFFIU0NnbkJodExtTWZIUnNXOFMxNmp2Q1FqTmJSUU8vTHA0aS9hMzdsWmNCa2tON0tiazM5TWovNU1EQU9LV0N0N3BKWTdGL0FtWW9YWmtVbUozTXc1QW41L3Z5Snc2VXBOSWVxZDFIcXNtRWhRWFZjOC9YblptajYvYVE4d04wWVZ0Rm1LbThhRS9JOUlxYjBlaHFYUm5GVG1uYW1RZTBURzlkeHBpOXh5MnZmWUdSSGJISGhQNDRlZmtXM1JLNVFOd1dIODV4OXR4VWhQNk44M1NycnRTL1pKa1NXUWc1RG85bHBZMTRZT01YUWY0RE5Xb3E1ZW5DSHdnMjRReTZVTWM2TzVSRmgiLCJtYWMiOiJhMjVhZDgxNzQ4ZDA4OWE5ZDkxMzBiNTY4YWI5ZTdkZGY2ZjBiOGRlMGJlZWNhMWIyNTVmMDFkOGZmY2Y5MTkwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-372719291\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1349910717 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349910717\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:16:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilh3SGoxZ3VEQ0J6eTFwaFMxTzhud3c9PSIsInZhbHVlIjoieHZqU2YveEw2dHFhMGViNVpwZHhnUHlJUk5ZQm1JQ2RWSHFjWGtFTjNtaWd1Z1FVNStNSG1oa1RLZy9OWFYraUQ5Unp3U25MKzhuRGw3UStFejVrMmh4R2o2bm1GN1VaQ0RENHJ1OHByb2VpTmN5eTZBUXNBNDNsVitBcWExaVNFMjRMNkhsY2lGK01BdWFINFgrb0V3K1czdnR2VFE0V2lYbk1KRXJsSU5mNTZUK0xGUFQzM1hwNUNlVUcveS85UHVvVU1wamR5bEpuU0cwbXVNVjZkd1RBK1JNUDBkZmJOS01GUDRrczNiSGNvYjJlbHFLbWNLN2NyTTRRcnBjTk12VFlvU3dnM2hzeXVvamxJVWRIOUlyR2ZUUjJadjB5YzZWQ01Bb0hDaTdhTVdmQnF3ZDJQYk9wK2hEUnNPcGU3UWlVN0s1bkEyZzh0cDF0NFBLSWl2NTZ0WXA4NU9nYnBEOWt5aWtoSThZekhvZGRmeUNLSzFrRzlTTHJpRXRpdHdFbkNnNWJZbVROZkpldGwvSlFnb2pSZVRSN1lUcXJMVXcvNFcyY0l4OTJGVHhxOE9Mb0dzaTE0K3FwcDFEWld0VzdILzJ3ZzRlUUI1TkVEMExrQTB6OFVkaEYraUdxODR2bFFtWW54SHVoSGJzdW5pelZUdy9xQWw3MXBPRXgiLCJtYWMiOiIyZjI3NzQ2NzkyZDI4YjhlZDUzZDUxZWM5YTUzZGRiZWZkODVhNWI0ZjUxNTBiMDA0ODQwZDEwYzI0NmE2NDQzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:16:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNPSEVmcDBVZHUybWxYcGlHR3hsZ0E9PSIsInZhbHVlIjoiMVZ5ZmRKMDNMK0JtdU93UHN1eDRlRHgwQWVRc3VrK0YzYXRZaGFBUTh5d0l4VlpUME5CMnNQaDJjckNVc05WVUdSeU93Y1BHMGtacGVUdVhnUUNNZGgrVm4yM3hIWEV4eE9GYVdPOFBFNDQxT1dyS1VYa1kxM040SExOaStwZlVmR01kaFBCa1hYcGRuemZnV2hFN3l2WFdocVUxT0RVMCszQUVTR09Vd3U3OFZBZEN6NDBQWUY0YzVrbTFiSFQ0TUh4eUkveXFiNkFnOW1jVmdrTEx5SmVLTm5waXFUZ2VjU2RkYUF6MVVNbE5GbFlpS0RIemR5WGprRFp3QVg0NTFqMXVGb24va2RYK1dNRG5UZzFLUXY4RURtS0VDR1VxSGQ1OGVYNE1WaU0zNzVMSTJvVlBBWTN6RVRGd0h1QURRWXlObUYvalJGS0dDK2ZDNExKVTc4NStWaVpNZVYzVkE4T0NsaW1XZXpIZnFOdG95NkZ4eVhiWnY5TmpBemhpMm1hL2l0YW8rZzQ2NTBFcTk1MkV3aWY2aXhiS3RORDZoam5OR2p3UnNmUWxiaE4zdUVXNFFVT21nN3pHSUhaenFiMHZmeXYvei9McDFEZmRmN3FqM0JmL2ZKaDZXK1RMUU15dkw5MUFleFJiQWdSMnJHc045Zmt1b01WWDdCNFMiLCJtYWMiOiI2YjM3MmQzMmNlYmI2M2I5MjJkYTc5MjQzYTViM2M5ODcyYjAyNDg1NzcxZjYxZjAzMDU0N2RiY2RjMzhmODYyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:16:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilh3SGoxZ3VEQ0J6eTFwaFMxTzhud3c9PSIsInZhbHVlIjoieHZqU2YveEw2dHFhMGViNVpwZHhnUHlJUk5ZQm1JQ2RWSHFjWGtFTjNtaWd1Z1FVNStNSG1oa1RLZy9OWFYraUQ5Unp3U25MKzhuRGw3UStFejVrMmh4R2o2bm1GN1VaQ0RENHJ1OHByb2VpTmN5eTZBUXNBNDNsVitBcWExaVNFMjRMNkhsY2lGK01BdWFINFgrb0V3K1czdnR2VFE0V2lYbk1KRXJsSU5mNTZUK0xGUFQzM1hwNUNlVUcveS85UHVvVU1wamR5bEpuU0cwbXVNVjZkd1RBK1JNUDBkZmJOS01GUDRrczNiSGNvYjJlbHFLbWNLN2NyTTRRcnBjTk12VFlvU3dnM2hzeXVvamxJVWRIOUlyR2ZUUjJadjB5YzZWQ01Bb0hDaTdhTVdmQnF3ZDJQYk9wK2hEUnNPcGU3UWlVN0s1bkEyZzh0cDF0NFBLSWl2NTZ0WXA4NU9nYnBEOWt5aWtoSThZekhvZGRmeUNLSzFrRzlTTHJpRXRpdHdFbkNnNWJZbVROZkpldGwvSlFnb2pSZVRSN1lUcXJMVXcvNFcyY0l4OTJGVHhxOE9Mb0dzaTE0K3FwcDFEWld0VzdILzJ3ZzRlUUI1TkVEMExrQTB6OFVkaEYraUdxODR2bFFtWW54SHVoSGJzdW5pelZUdy9xQWw3MXBPRXgiLCJtYWMiOiIyZjI3NzQ2NzkyZDI4YjhlZDUzZDUxZWM5YTUzZGRiZWZkODVhNWI0ZjUxNTBiMDA0ODQwZDEwYzI0NmE2NDQzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:16:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNPSEVmcDBVZHUybWxYcGlHR3hsZ0E9PSIsInZhbHVlIjoiMVZ5ZmRKMDNMK0JtdU93UHN1eDRlRHgwQWVRc3VrK0YzYXRZaGFBUTh5d0l4VlpUME5CMnNQaDJjckNVc05WVUdSeU93Y1BHMGtacGVUdVhnUUNNZGgrVm4yM3hIWEV4eE9GYVdPOFBFNDQxT1dyS1VYa1kxM040SExOaStwZlVmR01kaFBCa1hYcGRuemZnV2hFN3l2WFdocVUxT0RVMCszQUVTR09Vd3U3OFZBZEN6NDBQWUY0YzVrbTFiSFQ0TUh4eUkveXFiNkFnOW1jVmdrTEx5SmVLTm5waXFUZ2VjU2RkYUF6MVVNbE5GbFlpS0RIemR5WGprRFp3QVg0NTFqMXVGb24va2RYK1dNRG5UZzFLUXY4RURtS0VDR1VxSGQ1OGVYNE1WaU0zNzVMSTJvVlBBWTN6RVRGd0h1QURRWXlObUYvalJGS0dDK2ZDNExKVTc4NStWaVpNZVYzVkE4T0NsaW1XZXpIZnFOdG95NkZ4eVhiWnY5TmpBemhpMm1hL2l0YW8rZzQ2NTBFcTk1MkV3aWY2aXhiS3RORDZoam5OR2p3UnNmUWxiaE4zdUVXNFFVT21nN3pHSUhaenFiMHZmeXYvei9McDFEZmRmN3FqM0JmL2ZKaDZXK1RMUU15dkw5MUFleFJiQWdSMnJHc045Zmt1b01WWDdCNFMiLCJtYWMiOiI2YjM3MmQzMmNlYmI2M2I5MjJkYTc5MjQzYTViM2M5ODcyYjAyNDg1NzcxZjYxZjAzMDU0N2RiY2RjMzhmODYyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:16:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}